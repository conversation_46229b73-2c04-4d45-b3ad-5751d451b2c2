import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { MetricsService } from '@apply-goal-backend/monitoring';
import { AuditRepository } from './audit/audit.repository';
import {
  AuditLog,
  CreateAuditLogRequest,
  GetAuditLogRequest,
  ListAuditLogsRequest,
  SearchAuditLogsRequest,
  ListAuditLogsResponse,
  AuditLogResponse,
} from './audit/interfaces/audit.interface';

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);
  private auditLogCounter: any;
  private auditLogDuration: any;
  private auditLogsByServiceGauge: any;
  private auditLogsByactionsCounter: any;

  constructor(
    private readonly metricsService: MetricsService,
    private readonly auditRepository: AuditRepository
  ) {
    // Initialize metrics
    this.auditLogCounter = this.metricsService.createCounter(
      'audit_logs_total',
      'Total number of audit logs created',
      ['service_name', 'actions', 'resource_type']
    );

    this.auditLogDuration = this.metricsService.createHistogram(
      'audit_log_processing_duration_seconds',
      'Time spent processing audit logs',
      ['operation'],
      [0.1, 0.5, 1, 2, 5]
    );

    this.auditLogsByServiceGauge = this.metricsService.createGauge(
      'audit_logs_by_service',
      'Number of audit logs by service',
      ['service_name']
    );

    this.auditLogsByactionsCounter = this.metricsService.createCounter(
      'audit_logs_by_actions_total',
      'Total number of audit logs by actions type',
      ['actions']
    );
  }

  async createAuditLog(
    request: CreateAuditLogRequest
  ): Promise<AuditLogResponse> {
    const startTime = Date.now();
    try {
      // Ensure all required fields have values
      if (!request.userId) throw new Error('userId is required');
      if (!request.userRole) throw new Error('userRole is required');
      if (!request.actions) throw new Error('actions is required');
      if (!request.serviceName) throw new Error('serviceName is required');
      if (!request.resourceType) throw new Error('resourceType is required');
      if (!request.resourceId) throw new Error('resourceId is required');

      const log: Omit<AuditLog, 'id'> = {
        userId: request.userId,
        orgId: request.orgId,
        userRole: request.userRole,
        actions: request.actions,
        serviceName: request.serviceName,
        resourceType: request.resourceType,
        resourceId: request.resourceId,
        description: request.description || '',
        metaData: request.metaData || {},
        ipAddress: request.ipAddress || '',
        userAgent: request.userAgent || '',
        source: request.source || 'api',
        timeStamp: new Date(),
      };

      const createdLog = await this.auditRepository.create(log);

      // Track metrics
      this.auditLogCounter.inc({
        service_name: log.serviceName,
        actions: log.actions,
        resource_type: log.resourceType,
      });

      return {
        auditLog: {
          id: createdLog.id,
          userId: createdLog.userId,
          userRole: createdLog.userRole,
          actions: createdLog.actions,
          serviceName: createdLog.serviceName,
          resourceType: createdLog.resourceType,
          resourceId: createdLog.resourceId,
          description: createdLog.description,
          metadata: createdLog.metaData,
          ipAddress: createdLog.ipAddress,
          userAgent: createdLog.userAgent,
          createdAt: createdLog.timeStamp.getTime(),
          source: createdLog.source,
          timeStamp: createdLog.timeStamp,
        },
        message: 'Audit log created successfully',
      };
    } catch (error) {
      this.logger.error(
        `Failed to create audit log: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  async getAuditLog(request: GetAuditLogRequest): Promise<AuditLogResponse> {
    const startTime = Date.now();
    try {
      // Convert string ID to number if needed
      const id =
        typeof request.id === 'string' ? parseInt(request.id, 10) : request.id;

      const log = await this.auditRepository.findById(id);

      if (!log) {
        throw new NotFoundException(
          `Audit log with ID ${request.id} not found`
        );
      }

      // Format the response to match the proto definition
      const formattedLog = {
        id: Number(log.id),
        userId: Number(log.userId),
        userRole: log.userRole || '',
        actions: log.actions,
        serviceName: log.serviceName,
        resourceType: log.resourceType,
        resourceId: Number(log.resourceId),
        description: log.description,
        metadata: log.metaData || {},
        ipAddress: log.ipAddress || '',
        userAgent: log.userAgent || '',
        createdAt: log.timeStamp.getTime(),
        source: log.source || '',
        timeStamp: log.timeStamp,
      };

      return {
        auditLog: formattedLog,
        message: 'Audit log retrieved successfully',
      };
    } finally {
      const duration = (Date.now() - startTime) / 1000;
      this.auditLogDuration.observe({ operation: 'get' }, duration);
    }
  }

  async listAuditLogs(
    request: ListAuditLogsRequest
  ): Promise<ListAuditLogsResponse> {
    const startTime = Date.now();
    try {
      const pageSize = request.pageSize || 10;
      const pageToken = request.pageToken ? parseInt(request.pageToken, 10) : 0;
      const orderBy = request.orderBy || 'createdAt desc';

      // Parse order by string (field and direction)
      const [field, direction] = orderBy.split(' ');
      const orderDirection =
        direction?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

      // Map proto field names to database field names
      const fieldMap = {
        createdAt: 'timeStamp',
        userId: 'userId',
        serviceName: 'serviceName',
        action: 'actions',
        resourceType: 'resourceType',
        resourceId: 'resourceId',
      };

      const dbField = fieldMap[field] || 'timeStamp';

      // Get total count for pagination
      const totalCount = await this.auditRepository.count();

      // Get paginated logs with ordering
      const logs = await this.auditRepository.findPaginated(
        pageSize,
        pageToken,
        dbField,
        orderDirection
      );

      // Format logs to match proto definition
      const formattedLogs = logs.map((log) => ({
        id: Number(log.id),
        userId: Number(log.userId),
        serviceName: log.serviceName,
        actions: log.actions,
        resourceType: log.resourceType,
        resourceId: Number(log.resourceId),
        description: log.description,
        metadata: log.metaData || {},
        ipAddress: log.ipAddress || '',
        userAgent: log.userAgent || '',
        createdAt: log.timeStamp.getTime(),
      }));
      // Calculate next page token
      const nextPageToken =
        pageToken + pageSize < totalCount
          ? (pageToken + pageSize).toString()
          : undefined;

      return {
        auditLogs: formattedLogs,
        nextPageToken: nextPageToken,
        totalSize: totalCount,
      };
    } finally {
      const duration = (Date.now() - startTime) / 1000;
      this.auditLogDuration.observe({ operation: 'list' }, duration);
    }
  }

  async searchAuditLogs(
    request: SearchAuditLogsRequest
  ): Promise<ListAuditLogsResponse> {
    const startTime = Date.now();
    try {
      // Convert string IDs to numbers if needed
      const userId = request.userId
        ? typeof request.userId === 'string'
          ? parseInt(request.userId, 10)
          : request.userId
        : undefined;

      const resourceId = request.resourceId
        ? typeof request.resourceId === 'string'
          ? parseInt(request.resourceId, 10)
          : request.resourceId
        : undefined;

      // Build search criteria
      const searchCriteria = {
        userId,
        serviceName: request.serviceName,
        actions: request.action,
        resourceType: request.resourceType,
        resourceId,
        startTime: request.startDate ? new Date(request.startDate) : undefined,
        endTime: request.endDate ? new Date(request.endDate) : undefined,
      };

      // Get paginated search results
      const pageSize = request.pageSize || 10;
      const pageToken = request.pageToken ? parseInt(request.pageToken, 10) : 0;

      const { logs, totalCount } = await this.auditRepository.search(
        searchCriteria,
        pageSize,
        pageToken
      );

      // Format logs to match proto definition
      const formattedLogs = logs.map((log) => ({
        id: Number(log.id),
        userId: Number(log.userId),
        serviceName: log.serviceName,
        actions: log.actions, // corrected from 'action'
        resourceType: log.resourceType,
        resourceId: Number(log.resourceId),
        description: log.description,
        metadata: log.metaData || {},
        ipAddress: log.ipAddress || '',
        userAgent: log.userAgent || '',
        createdAt: log.timeStamp.getTime(),
      }));

      // Calculate next page token
      const nextPageToken =
        pageToken + pageSize < totalCount
          ? (pageToken + pageSize).toString()
          : undefined;

      return {
        auditLogs: formattedLogs,
        nextPageToken: nextPageToken,
        totalSize: totalCount,
      };
    } finally {
      const duration = (Date.now() - startTime) / 1000;
      this.auditLogDuration.observe({ operation: 'search' }, duration);
    }
  }

  // Helper method to update metrics
  private updateMetrics(log: AuditLog): void {
    this.auditLogCounter.inc({
      service_name: log.serviceName,
      actions: log.actions,
      resource_type: log.resourceType,
    });

    this.auditLogsByactionsCounter.inc({ actions: log.actions });

    // Update the gauge with the current count per service
    // Note: In a real implementation, you might want to maintain these counts more efficiently
    this.auditRepository.findAll().then((logs) => {
      const serviceCount = logs.filter(
        (l) => l.serviceName === log.serviceName
      ).length;
      this.auditLogsByServiceGauge.set(
        { service_name: log.serviceName },
        serviceCount
      );
    });
  }

  getData(): { message: string } {
    return { message: 'Audit Logging Service' };
  }
}
