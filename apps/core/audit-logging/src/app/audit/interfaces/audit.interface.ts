export interface AuditLog {
  id?: number;
  serviceName: string;
  userRole: string;
  actions: string;
  resourceType: string;
  resourceId: number;
  userId: number;
  orgId?: bigint;
  description: string;
  metaData?: Record<string, any>;
  source: string;
  timeStamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

export interface CreateAuditLogRequest
  extends Omit<AuditLog, 'id' | 'timeStamp'> {}

export interface GetAuditLogRequest {
  id: string;
}

export interface ListAuditLogsRequest {
  pageSize?: number;
  pageToken?: string;
  orderBy?: string;
}

export interface SearchAuditLogsRequest {
  userId?: number;
  serviceName?: string;
  action?: string;
  resourceType?: string;
  resourceId?: number;
  startDate?: number;
  endDate?: number;
  pageSize?: number;
  pageToken?: string;
}

export interface ListAuditLogsResponse {
  auditLogs: {
    id: number;
    userId: number;
    serviceName: string;
    actions: string;
    resourceType: string;
    resourceId: number;
    description: string;
    metadata: Record<string, string>;
    ipAddress: string;
    userAgent: string;
    createdAt: number;
  }[];
  nextPageToken?: string;
  totalSize: number;
}

export interface AuditLogResponse {
  auditLog: {
    id: number;
    userId: number;
    userRole: string;
    serviceName: string;
    actions: string;
    resourceType: string;
    resourceId: number;
    description: string;
    metadata: Record<string, string>;
    ipAddress: string;
    userAgent: string;
    createdAt: number;
    source: string;
    timeStamp: Date;
  };
  message: string;
}
