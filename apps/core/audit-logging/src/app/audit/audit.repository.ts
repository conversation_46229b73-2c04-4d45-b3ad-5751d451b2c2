import { Injectable } from '@nestjs/common';
import { AuditModel } from './audit.model';
import {
  AuditLog,
  CreateAuditLogRequest,
  SearchAuditLogsRequest,
} from './interfaces/audit.interface';
import { Op } from 'sequelize';

@Injectable()
export class AuditRepository {
  async create(data: CreateAuditLogRequest): Promise<AuditLog> {
    const created = await AuditModel.create({
      ...data,
      timeStamp: new Date(), // override to now
    });
    return created.toJSON() as AuditLog;
  }

  async findById(id: number): Promise<AuditLog | null> {
    const found = await AuditModel.findByPk(id);
    return found?.toJSON() as AuditLog | null;
  }

  async findAll(): Promise<AuditLog[]> {
    const results = await AuditModel.findAll();
    return results.map((log) => log.toJSON() as AuditLog);
  }

  async search(
    query: {
      userId?: number;
      orgId?: bigint;
      serviceName?: string;
      actions?: string;
      resourceType?: string;
      resourceId?: number;
      startTime?: Date;
      endTime?: Date;
    },
    limit: number = 10,
    offset: number = 0
  ): Promise<{ logs: AuditLog[]; totalCount: number }> {
    const where: any = {};

    if (query.userId) where.userId = query.userId;
    if (query.orgId) where.orgId = query.orgId;
    if (query.serviceName) where.serviceName = query.serviceName;
    if (query.actions) where.actions = query.actions;
    if (query.resourceType) where.resourceType = query.resourceType;
    if (query.resourceId) where.resourceId = query.resourceId;

    if (query.startTime || query.endTime) {
      where.timeStamp = {};
      if (query.startTime) where.timeStamp[Op.gte] = query.startTime;
      if (query.endTime) where.timeStamp[Op.lte] = query.endTime;
    }

    const { count, rows } = await AuditModel.findAndCountAll({
      where,
      limit,
      offset,
      order: [['timeStamp', 'DESC']],
    });

    return {
      logs: rows.map((log) => log.toJSON() as AuditLog),
      totalCount: count,
    };
  }

  async findPaginated(
    limit: number,
    offset: number,
    orderField: string = 'timeStamp',
    orderDirection: 'ASC' | 'DESC' = 'DESC'
  ): Promise<AuditLog[]> {
    const results = await AuditModel.findAll({
      limit,
      offset,
      order: [[orderField, orderDirection]],
    });
    return results.map((log) => log.toJSON() as AuditLog);
  }

  async count(): Promise<number> {
    return await AuditModel.count();
  }
}
