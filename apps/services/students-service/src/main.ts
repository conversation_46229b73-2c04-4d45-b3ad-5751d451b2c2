/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { join } from 'path';
import express from 'express';
import { healthRouter } from './routes/health.routes';
import { MetricsService, TracingService } from '@apply-goal-backend/monitoring';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Get monitoring services
  const metricsService = app.get(MetricsService);
  const tracingService = app.get(TracingService);

  // Start tracing
  await tracingService.start();

  // HTTP Middleware (Express)
  app.use(express.json());
  app.use('/health', healthRouter);

  // Setup gRPC Microservice
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.GRPC,
    options: {
      package: ['students', 'student_documents'],
      protoPath: [
        join(process.cwd(), 'libs/shared/dto/src/lib/students/students.proto'),
        join(process.cwd(), 'libs/shared/dto/src/lib/students/student-documents.proto'),
      ],
      url: '0.0.0.0:50058',
    },
  });

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);
  const port = process.env.PORT || 5008;
  await app.startAllMicroservices();
  await app.listen(port);

  Logger.log(
    `🚀 Students service is running on: http://localhost:${port}`
  );
  Logger.log(
    `📊 Metrics server is running on: http://localhost:${process.env.METRICS_PORT || '5008'}/metrics`
  );
}

bootstrap();
