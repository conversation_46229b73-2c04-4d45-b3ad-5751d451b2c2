# Student Documents Feature

This document describes the comprehensive Student Documents feature that allows uploading and managing flexible sets of student files through a multipart form request.

## Overview

The Student Documents feature provides:
- **Flexible file uploads** via multipart form data
- **MinIO/S3 integration** for file storage
- **Metadata persistence** in database
- **Document verification** workflow
- **gRPC API** for microservices communication
- **REST API** through API Gateway

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client/UI     │───▶│  Student-APIGW  │───▶│ Students-Service│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              │                        ▼
                              │                ┌─────────────────┐
                              │                │   MinIO/S3      │
                              │                └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   JWT Auth      │    │   PostgreSQL    │
                       └─────────────────┘    └─────────────────┘
```

## API Endpoints

### POST /students/:studentId/documents

Upload multiple documents for a student using multipart form data.

**Example cURL:**
```bash
curl -X POST 'http://student-api.localhost/students/123/documents' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -F 'academicSections=["ssc","hsc","bachelor"]' \
  -F 'proficiencySections=["duolingo","ielts"]' \
  -F 'sponsorName=<PERSON>' \
  -F 'takeDependents=true' \
  -F 'dependents=[{"name":"<PERSON>","passport":"X1234567"}]' \
  -F 'children=[{"name":"Jane Grey","passport":"Y7654321"}]' \
  \
  # profile  
  -F 'photo=@/path/to/photo.jpg' \
  -F 'signature=@/path/to/signature.png' \
  \
  # academic  
  -F 'ssc=@/path/to/ssc_certificate.pdf' \
  -F 'hsc=@/path/to/hsc_marksheet.pdf' \
  -F 'bachelor=@/path/to/bachelor_degree.pdf' \
  \
  # proficiency  
  -F 'duolingo=@/path/to/duolingo_certificate.pdf' \
  -F 'ielts=@/path/to/ielts_report.pdf' \
  \
  # sponsor  
  -F 'sponsorPhoto=@/path/to/sponsor_photo.jpg' \
  -F 'sponsorBankStatement=@/path/to/sponsor_bank_statement.pdf' \
  \
  # dependents (indexed fields)  
  -F 'dependents[0][photo]=@/path/to/dependent_photo.jpg' \
  -F 'dependents[0][passport]=@/path/to/dependent_passport.pdf' \
  \
  # children  
  -F 'children[0][photo]=@/path/to/child_photo.jpg' \
  -F 'children[0][passport]=@/path/to/child_passport.pdf'
```

### GET /students/:studentId/documents

Retrieve all documents for a student with optional filtering.

**Query Parameters:**
- `section` - Filter by section (profile, academic, proficiency, sponsor, dependent, child)
- `field` - Filter by field name
- `verificationStatus` - Filter by verification status
- `includeInactive` - Include soft-deleted documents

### GET /students/:studentId/documents/:documentId

Get a specific document by ID.

### PUT /students/:studentId/documents/:documentId/verification

Update document verification status.

### DELETE /students/:studentId/documents/:documentId

Soft delete a document.

### GET /students/:studentId/documents/sections/summary

Get a summary of documents grouped by section.

## Document Sections

### Profile Documents
- `photo` - Student profile photo
- `signature` - Student signature

### Academic Documents
- `ssc` - Secondary School Certificate
- `hsc` - Higher Secondary Certificate
- `bachelor` - Bachelor's degree
- `master` - Master's degree
- `phd` - PhD degree
- `diploma` - Diploma certificates
- `alevels` - A-Level certificates
- `olevels` - O-Level certificates

### Proficiency Documents
- `ielts` - IELTS test results
- `toefl` - TOEFL test results
- `pte` - PTE test results
- `duolingo` - Duolingo English Test
- `gre` - GRE test results
- `gmat` - GMAT test results
- `sat` - SAT test results

### Sponsor Documents
- `sponsorPhoto` - Sponsor photo
- `sponsorBankStatement` - Bank statements
- `sponsorIncome` - Income certificates
- Any field starting with "sponsor"

### Dependent/Child Documents
- `dependents[index][photo]` - Dependent photos
- `dependents[index][passport]` - Dependent passports
- `children[index][photo]` - Child photos
- `children[index][passport]` - Child passports

## Database Schema

### student_documents Table

```sql
CREATE TABLE student_documents (
  id BIGSERIAL PRIMARY KEY,
  student_id BIGINT NOT NULL REFERENCES students(id),
  section VARCHAR NOT NULL CHECK (section IN ('profile', 'academic', 'proficiency', 'sponsor', 'dependent', 'child', 'other')),
  field VARCHAR NOT NULL,
  filename VARCHAR NOT NULL,
  url VARCHAR NOT NULL,
  mime_type VARCHAR,
  file_size BIGINT,
  original_name VARCHAR,
  bucket VARCHAR,
  object_key VARCHAR,
  metadata JSONB,
  is_active BOOLEAN DEFAULT true,
  verification_status VARCHAR DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected', 'expired')),
  expiry_date DATE,
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Indexes
- `student_id` - For querying by student
- `section` - For filtering by document type
- `field` - For filtering by specific field
- `student_id, section, field` - Composite index for complex queries
- `verification_status` - For filtering by status
- `created_at` - For ordering by upload date

## File Storage Structure

Files are stored in MinIO/S3 with the following structure:

```
bucket/
├── students/
│   ├── {studentId}/
│   │   ├── profile/
│   │   │   ├── photo_2024-01-15_abc12345.jpg
│   │   │   └── signature_2024-01-15_def67890.png
│   │   ├── academic/
│   │   │   ├── ssc_2024-01-15_ghi11111.pdf
│   │   │   ├── hsc_2024-01-15_jkl22222.pdf
│   │   │   └── bachelor_2024-01-15_mno33333.pdf
│   │   ├── proficiency/
│   │   │   ├── ielts_2024-01-15_pqr44444.pdf
│   │   │   └── duolingo_2024-01-15_stu55555.pdf
│   │   ├── sponsor/
│   │   │   ├── sponsorPhoto_2024-01-15_vwx66666.jpg
│   │   │   └── sponsorBankStatement_2024-01-15_yz777777.pdf
│   │   ├── dependent/
│   │   │   ├── photo_2024-01-15_abc88888.jpg
│   │   │   └── passport_2024-01-15_def99999.pdf
│   │   └── child/
│   │       ├── photo_2024-01-15_ghi00000.jpg
│   │       └── passport_2024-01-15_jkl11111.pdf
```

## File Validation

### Allowed File Types
- **Images**: JPEG, JPG, PNG, GIF, BMP, WebP
- **Documents**: PDF, DOC, DOCX, TXT, RTF

### File Size Limits
- Default maximum: 10MB per file
- Configurable via environment variables

### Security
- File type validation based on MIME type
- Virus scanning (can be integrated)
- Access control via JWT authentication

## gRPC Integration

The feature includes full gRPC support for microservices communication:

### Proto Definition
```protobuf
service StudentDocumentsService {
  rpc UploadDocuments(UploadDocumentsRequest) returns (UploadDocumentsResponse);
  rpc GetStudentDocuments(GetStudentDocumentsRequest) returns (GetStudentDocumentsResponse);
  rpc GetDocument(GetDocumentRequest) returns (DocumentResponse);
  rpc UpdateDocumentVerification(UpdateDocumentVerificationRequest) returns (DocumentResponse);
  rpc DeleteDocument(DeleteDocumentRequest) returns (DeleteDocumentResponse);
  rpc GetDocumentsSummary(GetDocumentsSummaryRequest) returns (DocumentsSummaryResponse);
}
```

## Environment Variables

```env
# MinIO/S3 Configuration
S3_ENDPOINT=http://localhost:9000
S3_ACCESS_KEY=minioadmin
S3_SECRET_KEY=minioadmin123
S3_BUCKET=applygoal-files
S3_REGION=us-east-1
S3_FORCE_PATH_STYLE=true
S3_PUBLIC_ENDPOINT=http://localhost:9000

# Students Service gRPC
STUDENTS_SERVICE_URL=students-service:50053

# JWT Authentication
JWT_SECRET=applygoal-super-secret-jwt-key-2024
```

## Usage Examples

### TypeScript/JavaScript Client

```typescript
const formData = new FormData();

// Add metadata
formData.append('sponsorName', 'Philip Carter');
formData.append('takeDependents', 'true');
formData.append('academicSections', JSON.stringify(['ssc', 'hsc']));
formData.append('proficiencySections', JSON.stringify(['ielts']));

// Add files
formData.append('photo', photoFile);
formData.append('ssc', sscFile);
formData.append('ielts', ieltsFile);

const response = await fetch('/students/123/documents', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});
```

### React Component Example

```tsx
const DocumentUpload = ({ studentId }) => {
  const [files, setFiles] = useState({});
  
  const handleUpload = async () => {
    const formData = new FormData();
    
    // Add metadata
    formData.append('sponsorName', 'Philip Carter');
    formData.append('takeDependents', 'true');
    formData.append('academicSections', JSON.stringify(['ssc', 'hsc']));
    
    // Add files
    Object.entries(files).forEach(([key, file]) => {
      formData.append(key, file);
    });
    
    const response = await uploadDocuments(studentId, formData);
  };
  
  return (
    <form onSubmit={handleUpload}>
      <input type="file" onChange={(e) => setFiles({...files, photo: e.target.files[0]})} />
      <input type="file" onChange={(e) => setFiles({...files, ssc: e.target.files[0]})} />
      <button type="submit">Upload Documents</button>
    </form>
  );
};
```

## Testing

### Unit Tests
```bash
npm run test:unit -- student-documents
```

### Integration Tests
```bash
npm run test:integration -- student-documents
```

### E2E Tests
```bash
npm run test:e2e -- student-documents
```

## Deployment

1. **Run Migrations**
   ```bash
   npm run migration:run
   ```

2. **Start Services**
   ```bash
   docker-compose up students-service student-apigw minio
   ```

3. **Verify Health**
   ```bash
   curl http://localhost:4007/health
   curl http://localhost:5003/health
   ```

## Monitoring

- **Metrics**: Prometheus metrics for upload success/failure rates
- **Logging**: Structured logging with correlation IDs
- **Tracing**: Jaeger tracing for request flow
- **Alerts**: File upload failure alerts

## Security Considerations

1. **Authentication**: JWT-based authentication required
2. **Authorization**: Student-level access control
3. **File Validation**: MIME type and size validation
4. **Virus Scanning**: Integration with antivirus services
5. **Data Encryption**: Files encrypted at rest in MinIO
6. **Audit Trail**: All document operations logged

## Performance

- **Concurrent Uploads**: Supports multiple file uploads
- **Streaming**: Large file streaming support
- **Caching**: Document metadata caching
- **CDN**: Integration with CDN for file delivery
- **Compression**: Automatic image compression

## Troubleshooting

### Common Issues

1. **File Upload Fails**
   - Check MinIO connectivity
   - Verify file size limits
   - Check MIME type validation

2. **gRPC Connection Issues**
   - Verify service discovery
   - Check proto file compatibility
   - Validate service URLs

3. **Database Errors**
   - Run migrations
   - Check foreign key constraints
   - Verify table permissions

### Debug Commands

```bash
# Check MinIO connectivity
curl http://localhost:9000/minio/health/live

# Test gRPC service
grpcurl -plaintext localhost:50053 list

# Check database connection
npm run db:status
```
