import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { UploadModule } from '@apply-goal-backend/common';
import { StudentDocumentsController } from './controllers/student-documents.controller';
import { StudentDocumentsService } from './services/student-documents.service';
import { EnhancedUploadService } from './services/enhanced-upload.service';
import { StudentDocument } from './models/student-document.model';
import { Student } from './models/student.model';

@Module({
  imports: [
    SequelizeModule.forFeature([StudentDocument, Student]),
    UploadModule.forRoot({
      provider: 's3',
      s3: {
        endpoint: process.env.S3_ENDPOINT || 'http://localhost:9000',
        accessKey: process.env.S3_ACCESS_KEY || 'minioadmin',
        secretKey: process.env.S3_SECRET_KEY || 'minioadmin123',
        bucket: process.env.S3_BUCKET || 'applygoal-files',
        region: process.env.S3_REGION || 'us-east-1',
        forcePathStyle: process.env.S3_FORCE_PATH_STYLE === 'true' || true,
        publicEndpoint: process.env.S3_PUBLIC_ENDPOINT || process.env.S3_ENDPOINT || 'http://localhost:9000',
      },
    }),
  ],
  controllers: [StudentDocumentsController],
  providers: [StudentDocumentsService, EnhancedUploadService],
  exports: [StudentDocumentsService, EnhancedUploadService],
})
export class StudentDocumentsModule {}
