import { QueryInterface, DataTypes } from 'sequelize';

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 1. Create student_personal_info table
  await queryInterface.createTable('student_personal_info', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
    },
    student_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'students',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    firstName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    nameInNative: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    guardianPhone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    dateOfBirth: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    gender: {
      type: DataTypes.ENUM('male', 'female', 'other'),
      allowNull: true,
    },
    fatherName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    motherName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    nid: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
    },
    passport: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
    },
    presentAddress: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    permanentAddress: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    maritalStatus: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    sponsor: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    emergencyContact: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    preferredSubject: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    preferredCountry: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    reference: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    note: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes for student_personal_info
  await queryInterface.addIndex('student_personal_info', ['student_id'], { unique: true });
  await queryInterface.addIndex('student_personal_info', ['email'], { unique: true });
  await queryInterface.addIndex('student_personal_info', ['nid'], { unique: true });
  await queryInterface.addIndex('student_personal_info', ['passport'], { unique: true });

  // 2. Create student_academic_records table
  await queryInterface.createTable('student_academic_records', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
    },
    student_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'students',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    foreignDegree: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    nameOfExam: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    institute: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    subject: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    board: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    grade: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    passingYear: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    cgpa: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
    },
    percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
    },
    division: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    rollNumber: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    registrationNumber: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    examDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    certificateNumber: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    documents: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes for student_academic_records
  await queryInterface.addIndex('student_academic_records', ['student_id']);
  await queryInterface.addIndex('student_academic_records', ['nameOfExam']);
  await queryInterface.addIndex('student_academic_records', ['passingYear']);
  await queryInterface.addIndex('student_academic_records', ['institute']);

  // 3. Create student_proficiency_records table
  await queryInterface.createTable('student_proficiency_records', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
    },
    student_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'students',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    nameOfExam: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    score: {
      type: DataTypes.JSON,
      allowNull: false,
    },
    examDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    expiryDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    note: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    testCenter: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    candidateNumber: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    certificateNumber: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    testType: {
      type: DataTypes.ENUM('academic', 'general', 'professional'),
      allowNull: true,
    },
    band: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    level: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    documents: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes for student_proficiency_records
  await queryInterface.addIndex('student_proficiency_records', ['student_id']);
  await queryInterface.addIndex('student_proficiency_records', ['nameOfExam']);
  await queryInterface.addIndex('student_proficiency_records', ['examDate']);
  await queryInterface.addIndex('student_proficiency_records', ['expiryDate']);

  // 4. Create student_publications table
  await queryInterface.createTable('student_publications', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
    },
    student_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'students',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    subject: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    journal: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    publicationDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    link: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    abstract: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    authors: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    publicationType: {
      type: DataTypes.ENUM(
        'journal_article',
        'conference_paper',
        'book_chapter',
        'book',
        'thesis',
        'dissertation',
        'preprint',
        'patent',
        'other'
      ),
      allowNull: false,
      defaultValue: 'journal_article',
    },
    doi: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    isbn: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    issn: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    volume: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    issue: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    pages: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    publisher: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    conferenceLocation: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    conferenceDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    keywords: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    impactFactor: {
      type: DataTypes.DECIMAL(3, 2),
      allowNull: true,
    },
    citationCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
    },
    status: {
      type: DataTypes.ENUM('published', 'accepted', 'under_review', 'draft'),
      allowNull: false,
      defaultValue: 'published',
    },
    isPeerReviewed: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    isOpenAccess: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    documents: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes for student_publications
  await queryInterface.addIndex('student_publications', ['student_id']);
  await queryInterface.addIndex('student_publications', ['subject']);
  await queryInterface.addIndex('student_publications', ['journal']);
  await queryInterface.addIndex('student_publications', ['publicationDate']);
  await queryInterface.addIndex('student_publications', ['publicationType']);

  // 5. Create student_social_links table
  await queryInterface.createTable('student_social_links', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
    },
    student_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'students',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    platform: {
      type: DataTypes.ENUM(
        'linkedin',
        'facebook',
        'twitter',
        'instagram',
        'github',
        'portfolio',
        'youtube',
        'tiktok',
        'behance',
        'dribbble',
        'medium',
        'researchgate',
        'orcid',
        'googlescholar',
        'academia',
        'other'
      ),
      allowNull: false,
    },
    url: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    username: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    displayName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    isPublic: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    isPrimary: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    isProfessional: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    followerCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    followingCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    lastVerified: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    isVerified: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes for student_social_links
  await queryInterface.addIndex('student_social_links', ['student_id']);
  await queryInterface.addIndex('student_social_links', ['platform']);
  await queryInterface.addIndex('student_social_links', ['student_id', 'platform'], { unique: true });

  // 6. Create student_other_activities table
  await queryInterface.createTable('student_other_activities', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
    },
    student_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'students',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    subject: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    certificationLink: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    startDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    endDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    activityType: {
      type: DataTypes.ENUM(
        'certification',
        'course',
        'workshop',
        'seminar',
        'conference',
        'internship',
        'volunteer',
        'project',
        'competition',
        'award',
        'scholarship',
        'research',
        'training',
        'other'
      ),
      allowNull: false,
      defaultValue: 'certification',
    },
    organization: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    instructor: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    location: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    durationHours: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    certificateNumber: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    grade: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    score: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
    },
    isCertified: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    isOngoing: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    certificationExpiryDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    skills: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    technologies: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    cost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
    },
    currency: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    isSponsored: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    sponsor: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    documents: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes for student_other_activities
  await queryInterface.addIndex('student_other_activities', ['student_id']);
  await queryInterface.addIndex('student_other_activities', ['subject']);
  await queryInterface.addIndex('student_other_activities', ['activityType']);
  await queryInterface.addIndex('student_other_activities', ['startDate']);
  await queryInterface.addIndex('student_other_activities', ['endDate']);

  // 7. Update emergency_contacts table structure
  await queryInterface.addColumn('emergency_contacts', 'middleName', {
    type: DataTypes.STRING,
    allowNull: true,
  });

  await queryInterface.renameColumn('emergency_contacts', 'name', 'firstName');
  await queryInterface.addColumn('emergency_contacts', 'lastName', {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: '',
  });

  await queryInterface.renameColumn('emergency_contacts', 'phone', 'phoneHome');
  await queryInterface.addColumn('emergency_contacts', 'phoneMobile', {
    type: DataTypes.STRING,
    allowNull: true,
  });

  await queryInterface.changeColumn('emergency_contacts', 'email', {
    type: DataTypes.STRING,
    allowNull: true,
  });
};

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  // Drop tables in reverse order to handle foreign key constraints
  await queryInterface.dropTable('student_other_activities');
  await queryInterface.dropTable('student_social_links');
  await queryInterface.dropTable('student_publications');
  await queryInterface.dropTable('student_proficiency_records');
  await queryInterface.dropTable('student_academic_records');
  await queryInterface.dropTable('student_personal_info');

  // Revert emergency_contacts table changes
  await queryInterface.changeColumn('emergency_contacts', 'email', {
    type: DataTypes.STRING,
    allowNull: false,
  });

  await queryInterface.removeColumn('emergency_contacts', 'phoneMobile');
  await queryInterface.renameColumn('emergency_contacts', 'phoneHome', 'phone');
  await queryInterface.removeColumn('emergency_contacts', 'lastName');
  await queryInterface.renameColumn('emergency_contacts', 'firstName', 'name');
  await queryInterface.removeColumn('emergency_contacts', 'middleName');
};
