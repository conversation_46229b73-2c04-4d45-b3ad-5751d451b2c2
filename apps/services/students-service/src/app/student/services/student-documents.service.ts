import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { MulterFile } from '@apply-goal-backend/common';
import { StudentDocument } from '../models/student-document.model';
import { Student } from '../models/student.model';
import { EnhancedUploadService, EnhancedUploadResult } from './enhanced-upload.service';
import {
  CreateStudentDocumentsDto,
  CreateStudentDocumentsResponseDto,
  GetStudentDocumentsQueryDto,
  UpdateDocumentVerificationDto,
  StudentDocumentResponseDto,
} from '../dto/create-student-documents.dto';
import { Op } from 'sequelize';

@Injectable()
export class StudentDocumentsService {
  private readonly logger = new Logger(StudentDocumentsService.name);

  constructor(
    @InjectModel(StudentDocument)
    private readonly studentDocumentModel: typeof StudentDocument,
    @InjectModel(Student)
    private readonly studentModel: typeof Student,
    private readonly enhancedUploadService: EnhancedUploadService
  ) {}

  /**
   * Create or update student documents from multipart form upload
   */
  async createOrUpdate(
    studentId: number,
    metadataDto: CreateStudentDocumentsDto,
    uploadedFiles: { [fieldname: string]: MulterFile[] }
  ): Promise<CreateStudentDocumentsResponseDto> {
    this.logger.log(`Processing document upload for student ${studentId}`);

    // Verify student exists
    const student = await this.studentModel.findByPk(studentId);
    if (!student) {
      throw new NotFoundException(`Student with ID ${studentId} not found`);
    }

    // Validate all files before processing
    this.enhancedUploadService.validateFiles(uploadedFiles);

    const uploadResults: EnhancedUploadResult[] = [];
    const errors: string[] = [];

    try {
      // 1. Process regular files (profile, academic, proficiency, sponsor)
      const regularResults = await this.enhancedUploadService.uploadMultipleDocuments(
        uploadedFiles,
        studentId
      );
      uploadResults.push(...regularResults);

      // 2. Process dependent and children files with indexed metadata
      if (metadataDto.takeDependents && (metadataDto.dependents || metadataDto.children)) {
        const dependentResults = await this.enhancedUploadService.uploadDependentDocuments(
          uploadedFiles,
          studentId,
          metadataDto.dependents || [],
          metadataDto.children || []
        );
        uploadResults.push(...dependentResults);
      }

      // 6. Save all documents to database
      const savedDocuments = await this.saveDocumentsToDatabase(uploadResults, studentId);

      return {
        success: true,
        message: `Successfully uploaded ${savedDocuments.length} documents`,
        documents: savedDocuments.map(doc => this.mapToResponseDto(doc)),
        totalUploaded: savedDocuments.length,
        failedUploads: errors.length,
        errors: errors.length > 0 ? errors : undefined,
      };

    } catch (error) {
      this.logger.error(`Failed to process documents for student ${studentId}:`, error);
      throw new BadRequestException(`Document upload failed: ${error.message}`);
    }
  }

  /**
   * Get student documents with optional filtering
   */
  async getStudentDocuments(
    studentId: number,
    query: GetStudentDocumentsQueryDto
  ): Promise<StudentDocumentResponseDto[]> {
    const whereClause: any = { student_id: studentId };

    if (query.section) {
      whereClause.section = query.section;
    }

    if (query.field) {
      whereClause.field = query.field;
    }

    if (query.verificationStatus) {
      whereClause.verificationStatus = query.verificationStatus;
    }

    if (!query.includeInactive) {
      whereClause.isActive = true;
    }

    const documents = await this.studentDocumentModel.findAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
    });

    return documents.map(doc => this.mapToResponseDto(doc));
  }

  /**
   * Update document verification status
   */
  async updateDocumentVerification(
    documentId: number,
    updateDto: UpdateDocumentVerificationDto
  ): Promise<StudentDocumentResponseDto> {
    const document = await this.studentDocumentModel.findByPk(documentId);
    if (!document) {
      throw new NotFoundException(`Document with ID ${documentId} not found`);
    }

    await document.update({
      verificationStatus: updateDto.verificationStatus,
      notes: updateDto.notes,
      expiryDate: updateDto.expiryDate,
    });

    return this.mapToResponseDto(document);
  }

  /**
   * Delete a document (soft delete by setting isActive to false)
   */
  async deleteDocument(documentId: number): Promise<void> {
    const document = await this.studentDocumentModel.findByPk(documentId);
    if (!document) {
      throw new NotFoundException(`Document with ID ${documentId} not found`);
    }

    await document.update({ isActive: false });
  }

  // Private helper methods

  private async saveDocumentsToDatabase(
    uploadResults: EnhancedUploadResult[],
    studentId: number
  ): Promise<StudentDocument[]> {
    const documentsToCreate = uploadResults.map(result => ({
      student_id: studentId,
      section: this.determineSectionFromResult(result),
      field: this.extractFieldFromObjectKey(result.objectKey),
      filename: result.filename,
      url: result.url,
      mimeType: result.mimeType,
      fileSize: result.fileSize,
      originalName: result.originalName,
      bucket: result.bucket,
      objectKey: result.objectKey,
      metadata: (result as any).metadata || null,
      isActive: true,
      verificationStatus: 'pending' as const,
    }));

    return await this.studentDocumentModel.bulkCreate(documentsToCreate);
  }

  private determineSectionFromResult(result: EnhancedUploadResult): string {
    // Extract section from object key: students/{studentId}/{section}/{filename}
    const pathParts = result.objectKey.split('/');
    return pathParts[2] || 'other';
  }

  private extractFieldFromObjectKey(objectKey: string): string {
    // Extract field from filename in object key
    const pathParts = objectKey.split('/');
    const filename = pathParts[pathParts.length - 1];
    const fieldPart = filename.split('_')[0];
    return fieldPart;
  }

  private mapToResponseDto(document: StudentDocument): StudentDocumentResponseDto {
    return {
      id: Number(document.id),
      student_id: Number(document.student_id),
      section: document.section,
      field: document.field,
      filename: document.filename,
      url: document.url,
      mimeType: document.mimeType || '',
      fileSize: Number(document.fileSize || 0),
      originalName: document.originalName || '',
      bucket: document.bucket || '',
      objectKey: document.objectKey || '',
      metadata: document.metadata,
      verificationStatus: document.verificationStatus,
      created_at: document.createdAt,
      updated_at: document.updatedAt,
    };
  }
}
