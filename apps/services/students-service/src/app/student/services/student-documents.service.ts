import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { StudentDocument } from '../models/student-document.model';
import { Student } from '../models/student.model';
import { MinioService, DocumentUploadResult } from './minio.service';
import {
  CreateStudentDocumentsDto,
  CreateStudentDocumentsResponseDto,
  GetStudentDocumentsQueryDto,
  UpdateDocumentVerificationDto,
  StudentDocumentResponseDto,
} from '../dto/create-student-documents.dto';
import { Op } from 'sequelize';

@Injectable()
export class StudentDocumentsService {
  private readonly logger = new Logger(StudentDocumentsService.name);

  constructor(
    @InjectModel(StudentDocument)
    private readonly studentDocumentModel: typeof StudentDocument,
    @InjectModel(Student)
    private readonly studentModel: typeof Student,
    private readonly minioService: MinioService
  ) {}

  /**
   * Create or update student documents from multipart form upload
   */
  async createOrUpdate(
    studentId: number,
    metadataDto: CreateStudentDocumentsDto,
    uploadedFiles: { [fieldname: string]: Express.Multer.File[] }
  ): Promise<CreateStudentDocumentsResponseDto> {
    this.logger.log(`Processing document upload for student ${studentId}`);

    // Verify student exists
    const student = await this.studentModel.findByPk(studentId);
    if (!student) {
      throw new NotFoundException(`Student with ID ${studentId} not found`);
    }

    // Validate all files before processing
    this.minioService.validateFiles(uploadedFiles);

    const uploadResults: DocumentUploadResult[] = [];
    const errors: string[] = [];

    try {
      // 1. Process profile files (photo, signature)
      const profileResults = await this.processProfileFiles(uploadedFiles, studentId);
      uploadResults.push(...profileResults);

      // 2. Process academic files
      const academicResults = await this.processAcademicFiles(
        uploadedFiles,
        studentId,
        metadataDto.academicSections
      );
      uploadResults.push(...academicResults);

      // 3. Process proficiency files
      const proficiencyResults = await this.processProficiencyFiles(
        uploadedFiles,
        studentId,
        metadataDto.proficiencySections
      );
      uploadResults.push(...proficiencyResults);

      // 4. Process sponsor files
      const sponsorResults = await this.processSponsorFiles(uploadedFiles, studentId);
      uploadResults.push(...sponsorResults);

      // 5. Process dependent and children files
      if (metadataDto.takeDependents && (metadataDto.dependents || metadataDto.children)) {
        const dependentResults = await this.minioService.uploadDependentFiles(
          uploadedFiles,
          studentId,
          metadataDto.dependents || [],
          metadataDto.children || []
        );
        uploadResults.push(...dependentResults);
      }

      // 6. Save all documents to database
      const savedDocuments = await this.saveDocumentsToDatabase(uploadResults, studentId, metadataDto);

      return {
        success: true,
        message: `Successfully uploaded ${savedDocuments.length} documents`,
        documents: savedDocuments.map(doc => this.mapToResponseDto(doc)),
        totalUploaded: savedDocuments.length,
        failedUploads: errors.length,
        errors: errors.length > 0 ? errors : undefined,
      };

    } catch (error) {
      this.logger.error(`Failed to process documents for student ${studentId}:`, error);
      throw new BadRequestException(`Document upload failed: ${error.message}`);
    }
  }

  /**
   * Get student documents with optional filtering
   */
  async getStudentDocuments(
    studentId: number,
    query: GetStudentDocumentsQueryDto
  ): Promise<StudentDocumentResponseDto[]> {
    const whereClause: any = { student_id: studentId };

    if (query.section) {
      whereClause.section = query.section;
    }

    if (query.field) {
      whereClause.field = query.field;
    }

    if (query.verificationStatus) {
      whereClause.verificationStatus = query.verificationStatus;
    }

    if (!query.includeInactive) {
      whereClause.isActive = true;
    }

    const documents = await this.studentDocumentModel.findAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
    });

    return documents.map(doc => this.mapToResponseDto(doc));
  }

  /**
   * Update document verification status
   */
  async updateDocumentVerification(
    documentId: number,
    updateDto: UpdateDocumentVerificationDto
  ): Promise<StudentDocumentResponseDto> {
    const document = await this.studentDocumentModel.findByPk(documentId);
    if (!document) {
      throw new NotFoundException(`Document with ID ${documentId} not found`);
    }

    await document.update({
      verificationStatus: updateDto.verificationStatus,
      notes: updateDto.notes,
      expiryDate: updateDto.expiryDate,
    });

    return this.mapToResponseDto(document);
  }

  /**
   * Delete a document (soft delete by setting isActive to false)
   */
  async deleteDocument(documentId: number): Promise<void> {
    const document = await this.studentDocumentModel.findByPk(documentId);
    if (!document) {
      throw new NotFoundException(`Document with ID ${documentId} not found`);
    }

    await document.update({ isActive: false });
  }

  // Private helper methods

  private async processProfileFiles(
    files: { [fieldname: string]: Express.Multer.File[] },
    studentId: number
  ): Promise<DocumentUploadResult[]> {
    const results: DocumentUploadResult[] = [];
    const profileFields = ['photo', 'signature'];

    for (const field of profileFields) {
      if (files[field]) {
        for (const file of files[field]) {
          try {
            const result = await this.minioService.uploadFile(
              file.buffer,
              file.originalname,
              file.mimetype,
              'profile',
              field,
              studentId
            );
            results.push(result);
          } catch (error) {
            this.logger.error(`Failed to upload profile file ${field}:`, error);
          }
        }
      }
    }

    return results;
  }

  private async processAcademicFiles(
    files: { [fieldname: string]: Express.Multer.File[] },
    studentId: number,
    academicSections: string[]
  ): Promise<DocumentUploadResult[]> {
    const results: DocumentUploadResult[] = [];

    for (const section of academicSections) {
      if (files[section]) {
        for (const file of files[section]) {
          try {
            const result = await this.minioService.uploadFile(
              file.buffer,
              file.originalname,
              file.mimetype,
              'academic',
              section,
              studentId
            );
            results.push(result);
          } catch (error) {
            this.logger.error(`Failed to upload academic file ${section}:`, error);
          }
        }
      }
    }

    return results;
  }

  private async processProficiencyFiles(
    files: { [fieldname: string]: Express.Multer.File[] },
    studentId: number,
    proficiencySections: string[]
  ): Promise<DocumentUploadResult[]> {
    const results: DocumentUploadResult[] = [];

    for (const section of proficiencySections) {
      if (files[section]) {
        for (const file of files[section]) {
          try {
            const result = await this.minioService.uploadFile(
              file.buffer,
              file.originalname,
              file.mimetype,
              'proficiency',
              section,
              studentId
            );
            results.push(result);
          } catch (error) {
            this.logger.error(`Failed to upload proficiency file ${section}:`, error);
          }
        }
      }
    }

    return results;
  }

  private async processSponsorFiles(
    files: { [fieldname: string]: Express.Multer.File[] },
    studentId: number
  ): Promise<DocumentUploadResult[]> {
    const results: DocumentUploadResult[] = [];
    
    // Look for any field that starts with "sponsor"
    for (const [fieldname, fileArray] of Object.entries(files)) {
      if (fieldname.toLowerCase().startsWith('sponsor')) {
        for (const file of fileArray) {
          try {
            const result = await this.minioService.uploadFile(
              file.buffer,
              file.originalname,
              file.mimetype,
              'sponsor',
              fieldname,
              studentId
            );
            results.push(result);
          } catch (error) {
            this.logger.error(`Failed to upload sponsor file ${fieldname}:`, error);
          }
        }
      }
    }

    return results;
  }

  private async saveDocumentsToDatabase(
    uploadResults: DocumentUploadResult[],
    studentId: number,
    metadataDto: CreateStudentDocumentsDto
  ): Promise<StudentDocument[]> {
    const documentsToCreate = uploadResults.map(result => ({
      student_id: studentId,
      section: this.determineSectionFromResult(result),
      field: this.extractFieldFromObjectKey(result.objectKey),
      filename: result.filename,
      url: result.url,
      mimeType: result.mimeType,
      fileSize: result.fileSize,
      originalName: result.originalName,
      bucket: result.bucket,
      objectKey: result.objectKey,
      metadata: (result as any).metadata || null,
      isActive: true,
      verificationStatus: 'pending' as const,
    }));

    return await this.studentDocumentModel.bulkCreate(documentsToCreate);
  }

  private determineSectionFromResult(result: DocumentUploadResult): string {
    // Extract section from object key: students/{studentId}/{section}/{filename}
    const pathParts = result.objectKey.split('/');
    return pathParts[2] || 'other';
  }

  private extractFieldFromObjectKey(objectKey: string): string {
    // Extract field from filename in object key
    const pathParts = objectKey.split('/');
    const filename = pathParts[pathParts.length - 1];
    const fieldPart = filename.split('_')[0];
    return fieldPart;
  }

  private mapToResponseDto(document: StudentDocument): StudentDocumentResponseDto {
    return {
      id: Number(document.id),
      student_id: Number(document.student_id),
      section: document.section,
      field: document.field,
      filename: document.filename,
      url: document.url,
      mimeType: document.mimeType || '',
      fileSize: Number(document.fileSize || 0),
      originalName: document.originalName || '',
      bucket: document.bucket || '',
      objectKey: document.objectKey || '',
      metadata: document.metadata,
      created_at: document.created_at,
      updated_at: document.updated_at,
    };
  }
}
