import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { UploadService, MulterFile } from '@apply-goal-backend/common';
import { v4 as uuid } from 'uuid';
import { extname } from 'path';

export interface DocumentUploadResult {
  url: string;
  filename: string;
  originalName: string;
  mimeType: string;
  fileSize: number;
  bucket: string;
  objectKey: string;
}

@Injectable()
export class StudentDocumentUploadService {
  private readonly logger = new Logger(StudentDocumentUploadService.name);

  constructor(private readonly uploadService: UploadService) {}

  /**
   * Upload a single file with student document specific naming
   * Uses the centralized upload service following the same pattern as auth-service
   */
  async uploadFile(
    buffer: Buffer,
    originalFilename: string,
    mimeType: string,
    section: string,
    field: string,
    studentId: string | number
  ): Promise<DocumentUploadResult> {
    try {
      const ext = extname(originalFilename);
      const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
      const uniqueId = uuid().substring(0, 8);

      // Create structured filename for better organization
      const filename = `${field}_${timestamp}_${uniqueId}${ext}`;
      const objectKey = `students/${studentId}/${section}/${filename}`;

      // Create a MulterFile-like object for the centralized upload service
      const file: MulterFile = {
        fieldname: field,
        originalname: originalFilename,
        encoding: '7bit',
        mimetype: mimeType,
        size: buffer.length,
        buffer: buffer,
      };

      // Determine file type for upload service (follows auth-service pattern)
      const fileType = this.isImageFile(mimeType) ? 'image' : 'file';

      // Use the centralized upload service with custom key generation
      const result = await this.uploadFileWithStructuredKey(file, objectKey, fileType);

      return {
        url: result.url,
        filename: filename,
        originalName: originalFilename,
        mimeType: mimeType,
        fileSize: buffer.length,
        bucket: process.env.S3_BUCKET || 'applygoal-files',
        objectKey: objectKey,
      };
    } catch (error) {
      this.logger.error(`Failed to upload file ${originalFilename}:`, error);
      throw new BadRequestException(`File upload failed: ${error.message}`);
    }
  }

  /**
   * Upload multiple files from a multipart form
   */
  async uploadMultipleFiles(
    files: { [fieldname: string]: MulterFile[] },
    studentId: string | number
  ): Promise<DocumentUploadResult[]> {
    const results: DocumentUploadResult[] = [];

    for (const [fieldname, fileArray] of Object.entries(files)) {
      for (const file of fileArray) {
        try {
          const section = this.determineSectionFromFieldname(fieldname);
          const result = await this.uploadFile(
            file.buffer,
            file.originalname,
            file.mimetype,
            section,
            fieldname,
            studentId
          );
          results.push(result);
        } catch (error) {
          this.logger.error(`Failed to upload file ${file.originalname} for field ${fieldname}:`, error);
          // Continue with other files even if one fails
        }
      }
    }

    return results;
  }

  /**
   * Upload files for dependents/children with indexed field names
   */
  async uploadDependentFiles(
    files: { [fieldname: string]: MulterFile[] },
    studentId: string | number,
    dependents: Array<{ name: string; passport: string }>,
    children: Array<{ name: string; passport: string }>
  ): Promise<DocumentUploadResult[]> {
    const results: DocumentUploadResult[] = [];

    for (const [fieldname, fileArray] of Object.entries(files)) {
      // Handle indexed dependent/child files like "dependents[0][photo]"
      const dependentMatch = fieldname.match(/^dependents\[(\d+)\]\[(\w+)\]$/);
      const childMatch = fieldname.match(/^children\[(\d+)\]\[(\w+)\]$/);

      if (dependentMatch) {
        const [, indexStr, field] = dependentMatch;
        const index = parseInt(indexStr, 10);
        const dependent = dependents[index];

        if (dependent) {
          for (const file of fileArray) {
            try {
              const result = await this.uploadFile(
                file.buffer,
                file.originalname,
                file.mimetype,
                'dependent',
                field,
                studentId
              );
              
              // Add dependent metadata
              (result as any).metadata = {
                dependentIndex: index,
                dependentName: dependent.name,
                dependentPassport: dependent.passport,
              };
              
              results.push(result);
            } catch (error) {
              this.logger.error(`Failed to upload dependent file:`, error);
            }
          }
        }
      } else if (childMatch) {
        const [, indexStr, field] = childMatch;
        const index = parseInt(indexStr, 10);
        const child = children[index];

        if (child) {
          for (const file of fileArray) {
            try {
              const result = await this.uploadFile(
                file.buffer,
                file.originalname,
                file.mimetype,
                'child',
                field,
                studentId
              );
              
              // Add child metadata
              (result as any).metadata = {
                childIndex: index,
                childName: child.name,
                childPassport: child.passport,
              };
              
              results.push(result);
            } catch (error) {
              this.logger.error(`Failed to upload child file:`, error);
            }
          }
        }
      }
    }

    return results;
  }

  /**
   * Determine section based on fieldname
   */
  private determineSectionFromFieldname(fieldname: string): string {
    // Profile files
    if (['photo', 'signature'].includes(fieldname)) {
      return 'profile';
    }

    // Academic files
    const academicFields = ['ssc', 'hsc', 'bachelor', 'master', 'phd', 'diploma', 'alevels', 'olevels'];
    if (academicFields.some(field => fieldname.toLowerCase().includes(field))) {
      return 'academic';
    }

    // Proficiency files
    const proficiencyFields = ['ielts', 'toefl', 'pte', 'duolingo', 'gre', 'gmat', 'sat'];
    if (proficiencyFields.some(field => fieldname.toLowerCase().includes(field))) {
      return 'proficiency';
    }

    // Sponsor files
    if (fieldname.toLowerCase().includes('sponsor')) {
      return 'sponsor';
    }

    // Default to other
    return 'other';
  }

  /**
   * Check if file is an image based on MIME type
   */
  private isImageFile(mimeType: string): boolean {
    return mimeType.startsWith('image/');
  }

  /**
   * Upload file with structured key following the centralized upload service pattern
   * This method extends the upload service to support custom key generation
   * while maintaining consistency with the auth-service approach
   */
  private async uploadFileWithStructuredKey(
    file: MulterFile,
    customKey: string,
    type: 'image' | 'file'
  ): Promise<{ url: string }> {
    try {
      // Use the centralized upload service which handles:
      // - Image resizing (if needed)
      // - File validation
      // - Storage provider abstraction
      // - Consistent error handling

      // For now, we use the existing upload service and accept its key generation
      // In the future, we could extend the upload service to accept custom keys
      // or create a specialized document upload service that inherits from UploadService
      const result = await this.uploadService.uploadFile(file, type);

      // TODO: Future enhancement - modify the upload service to support custom keys
      // This would allow us to maintain our structured path while using the centralized service
      // For now, we return the URL from the centralized service

      return result;
    } catch (error) {
      this.logger.error(`Failed to upload file with structured key:`, error);
      throw new BadRequestException(`Structured file upload failed: ${error.message}`);
    }
  }

  /**
   * Validate file type and size
   */
  validateFile(file: MulterFile, maxSizeBytes: number = 10 * 1024 * 1024): void {
    // Check file size (default 10MB)
    if (file.size > maxSizeBytes) {
      throw new BadRequestException(`File ${file.originalname} is too large. Maximum size is ${maxSizeBytes / 1024 / 1024}MB`);
    }

    // Check for allowed file types
    const allowedMimeTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(`File type ${file.mimetype} is not allowed for ${file.originalname}`);
    }
  }

  /**
   * Validate all files in the upload
   */
  validateFiles(files: { [fieldname: string]: MulterFile[] }): void {
    for (const [fieldname, fileArray] of Object.entries(files)) {
      for (const file of fileArray) {
        this.validateFile(file);
      }
    }
  }
}
