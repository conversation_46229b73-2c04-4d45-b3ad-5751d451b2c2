import { EmergencyContact } from './emergency-contact.model';
import { Enrollment } from './enrollment.model';
import { Grade } from './grade.model';
import { StudentAcademicRecord } from './student-academic-record.model';
import { StudentAcademicBackground } from './student-academic.model';
import { StudentDocument } from './student-document.model';
import { StudentOtherActivity } from './student-other-activity.model';
import { StudentPersonalInfo } from './student-personal-info.model';
import { StudentProficiencyRecord } from './student-proficiency-record.model';
import { StudentPublication } from './student-publication.model';
import { StudentSocialLink } from './student-social-link.model';
import { Student } from './student.model';

// Main Student Model
export { Student } from './student.model';

// Personal Information
export { StudentPersonalInfo } from './student-personal-info.model';

// Academic Information
export { StudentAcademicRecord } from './student-academic-record.model';
export { StudentProficiencyRecord } from './student-proficiency-record.model';
export { StudentPublication } from './student-publication.model';
export { StudentOtherActivity } from './student-other-activity.model';

// Social and Contact Information
export { StudentSocialLink } from './student-social-link.model';
export { EmergencyContact } from './emergency-contact.model';

// Documents
export { StudentDocument } from './student-document.model';

// Existing Models
export { Enrollment } from './enrollment.model';
export { Grade } from './grade.model';
export { StudentAcademicBackground } from './student-academic.model';

// Model Arrays for easy import
export const StudentModels = [
  Student,
  StudentPersonalInfo,
  StudentAcademicRecord,
  StudentProficiencyRecord,
  StudentPublication,
  StudentOtherActivity,
  StudentSocialLink,
  StudentDocument,
  EmergencyContact,
  Enrollment,
  Grade,
  StudentAcademicBackground,
];

export const NewStudentModels = [
  StudentPersonalInfo,
  StudentAcademicRecord,
  StudentProficiencyRecord,
  StudentPublication,
  StudentOtherActivity,
  StudentSocialLink,
  StudentDocument,
];
