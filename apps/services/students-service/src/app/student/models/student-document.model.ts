import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  BelongsTo,
  ForeignKey,
  Table,
  Index,
  Default,
} from 'sequelize-typescript';
import { Student } from './student.model';

@Table({
  tableName: 'student_documents',
  indexes: [
    { fields: ['student_id'] },
    { fields: ['section'] },
    { fields: ['field'] },
    { fields: ['student_id', 'section', 'field'] },
    { fields: ['created_at'] },
  ],
})
export class StudentDocument extends BaseModel {
  @ForeignKey(() => Student)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  student_id!: bigint;

  @Column({
    type: DataType.ENUM(
      'profile',
      'academic',
      'proficiency',
      'sponsor',
      'dependent',
      'child',
      'other'
    ),
    allowNull: false,
  })
  section!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: 'Field name like photo, signature, ssc, hsc, ielts, etc.',
  })
  field!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  filename!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: 'Full URL to the uploaded file in MinIO',
  })
  url!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mimeType?: string;

  @Column({
    type: DataType.BIGINT,
    allowNull: true,
    comment: 'File size in bytes',
  })
  fileSize?: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Original filename from upload',
  })
  originalName?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'MinIO bucket name',
  })
  bucket?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'MinIO object key/path',
  })
  objectKey?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
    comment: 'Additional metadata like dependent/child info',
  })
  metadata?: {
    dependentIndex?: number;
    childIndex?: number;
    dependentName?: string;
    childName?: string;
    dependentPassport?: string;
    childPassport?: string;
    [key: string]: any;
  };

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  isActive!: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Document verification status',
  })
  verificationStatus?: 'pending' | 'verified' | 'rejected' | 'expired';

  @Column({
    type: DataType.DATEONLY,
    allowNull: true,
    comment: 'Document expiry date if applicable',
  })
  expiryDate?: Date;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  notes?: string;

  @BelongsTo(() => Student)
  student!: Student;

  // Virtual properties
  get isExpired(): boolean {
    if (!this.expiryDate) return false;
    return new Date() > new Date(this.expiryDate);
  }

  get isVerified(): boolean {
    return this.verificationStatus === 'verified';
  }

  get isPending(): boolean {
    return this.verificationStatus === 'pending' || !this.verificationStatus;
  }

  get isProfileDocument(): boolean {
    return this.section === 'profile';
  }

  get isAcademicDocument(): boolean {
    return this.section === 'academic';
  }

  get isProficiencyDocument(): boolean {
    return this.section === 'proficiency';
  }

  get isSponsorDocument(): boolean {
    return this.section === 'sponsor';
  }

  get isDependentDocument(): boolean {
    return this.section === 'dependent';
  }

  get isChildDocument(): boolean {
    return this.section === 'child';
  }

  get fileExtension(): string {
    return this.filename.split('.').pop()?.toLowerCase() || '';
  }

  get isImage(): boolean {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    return imageExtensions.includes(this.fileExtension);
  }

  get isPDF(): boolean {
    return this.fileExtension === 'pdf';
  }

  get isDocument(): boolean {
    const docExtensions = ['pdf', 'doc', 'docx', 'txt', 'rtf'];
    return docExtensions.includes(this.fileExtension);
  }

  get fileSizeFormatted(): string {
    if (!this.fileSize) return 'Unknown';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = this.fileSize;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  get displayName(): string {
    if (this.isDependentDocument && this.metadata?.dependentName) {
      return `${this.metadata.dependentName} - ${this.field}`;
    }
    if (this.isChildDocument && this.metadata?.childName) {
      return `${this.metadata.childName} - ${this.field}`;
    }
    return `${this.section} - ${this.field}`;
  }
}
