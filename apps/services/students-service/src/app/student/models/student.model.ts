import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  HasMany,
  HasOne,
  Table,
  Index,
  Default,
} from 'sequelize-typescript';
import { EmergencyContact } from './emergency-contact.model';
import { Enrollment } from './enrollment.model';
import { Grade } from './grade.model';
import { StudentPersonalInfo } from './student-personal-info.model';
import { StudentAcademicRecord } from './student-academic-record.model';
import { StudentProficiencyRecord } from './student-proficiency-record.model';
import { StudentPublication } from './student-publication.model';
import { StudentSocialLink } from './student-social-link.model';
import { StudentOtherActivity } from './student-other-activity.model';

@Table({
  tableName: 'students',
  indexes: [
    { unique: true, fields: ['student_id'] },
  ],
})
export class Student extends BaseModel {
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  user_id?: bigint;

  @Column({
    type: DataType.STRING,
    unique: true,
    allowNull: false,
  })
  student_id!: string;

  @Column({
    type: DataType.ENUM(
      'freshman',
      'sophomore',
      'junior',
      'senior',
      'graduate',
      'postgraduate'
    ),
    allowNull: false,
    defaultValue: 'freshman',
  })
  academic_level!: string;

  @Column({
    type: DataType.DECIMAL(3, 2),
    allowNull: true,
    defaultValue: 0.0,
  })
  gpa?: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
  })
  total_credits?: number;

  @Column({
    type: DataType.ENUM(
      'active',
      'inactive',
      'graduated',
      'on_leave',
      'withdrawn',
      'suspended'
    ),
    allowNull: false,
    defaultValue: 'active',
  })
  status!: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true,
  })
  enrollment_date?: Date;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true,
  })
  graduation_date?: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  university_id?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  agency_id?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  metadata?: Record<string, any>;

  // Relationships
  @HasOne(() => StudentPersonalInfo)
  personalInfo!: StudentPersonalInfo;

  @HasMany(() => StudentAcademicRecord)
  academicRecords!: StudentAcademicRecord[];

  @HasMany(() => StudentProficiencyRecord)
  proficiencyRecords!: StudentProficiencyRecord[];

  @HasMany(() => StudentPublication)
  publications!: StudentPublication[];

  @HasMany(() => StudentSocialLink)
  socialLinks!: StudentSocialLink[];

  @HasMany(() => StudentOtherActivity)
  otherActivities!: StudentOtherActivity[];

  @HasMany(() => Enrollment)
  enrollments!: Enrollment[];

  @HasMany(() => Grade)
  grades!: Grade[];

  @HasMany(() => EmergencyContact)
  emergency_contacts!: EmergencyContact[];

  // Virtual properties
  get full_name(): string {
    return this.personalInfo
      ? `${this.personalInfo.firstName} ${this.personalInfo.lastName}`
      : 'Unknown';
  }

  get is_active(): boolean {
    return this.status === 'active';
  }

  get is_graduated(): boolean {
    return this.status === 'graduated';
  }

  get age(): number {
    if (!this.personalInfo?.dateOfBirth) return 0;
    const today = new Date();
    const birthDate = new Date(this.personalInfo.dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  }

  get academic_standing(): string {
    if (!this.gpa) return 'Unknown';

    if (this.gpa >= 3.5) return "Dean's List";
    if (this.gpa >= 3.0) return 'Good Standing';
    if (this.gpa >= 2.0) return 'Satisfactory';
    if (this.gpa >= 1.0) return 'Probation';
    return 'Academic Warning';
  }

  get credits_to_graduation(): number {
    const requiredCredits =
      this.academic_level === 'graduate' ||
      this.academic_level === 'postgraduate'
        ? 30
        : 120;
    return Math.max(0, requiredCredits - (this.total_credits || 0));
  }
}
