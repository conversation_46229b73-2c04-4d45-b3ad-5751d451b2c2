import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  BelongsTo,
  ForeignKey,
  Table,
  Default,
} from 'sequelize-typescript';
import { Student } from './student.model';

@Table({
  tableName: 'emergency_contacts',
  indexes: [
    { fields: ['student_id'] },
    { fields: ['relationship'] },
  ]
})
export class EmergencyContact extends BaseModel {
  @ForeignKey(() => Student)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  student_id!: bigint;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  firstName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  middleName?: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  lastName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: 'parent, guardian, spouse, sibling, friend, etc.',
  })
  relationship!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  phoneHome!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  phoneMobile?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  email?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  address?: {
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };

  @Default(true)
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  is_primary!: boolean;

  @Default(true)
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    comment: 'Can pick up student in emergency',
  })
  can_pick_up!: boolean;

  @Default(true)
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    comment: 'Can authorize medical treatment',
  })
  can_authorize_medical!: boolean;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  notes?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  metadata?: Record<string, any>;

  @BelongsTo(() => Student)
  student!: Student;

  // Virtual properties
  get fullName(): string {
    return this.middleName
      ? `${this.firstName} ${this.middleName} ${this.lastName}`
      : `${this.firstName} ${this.lastName}`;
  }

  get full_contact_info(): string {
    const phone = this.phoneMobile || this.phoneHome;
    const email = this.email || 'No email';
    return `${this.fullName} (${this.relationship}) - ${phone} - ${email}`;
  }

  get is_family(): boolean {
    const familyRelationships = ['parent', 'guardian', 'spouse', 'sibling', 'child', 'mother', 'father'];
    return familyRelationships.includes(this.relationship.toLowerCase());
  }

  get primaryPhone(): string {
    return this.phoneMobile || this.phoneHome;
  }

  get hasEmail(): boolean {
    return !!this.email;
  }

  get hasMobilePhone(): boolean {
    return !!this.phoneMobile;
  }
}
