import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  BelongsTo,
  ForeignKey,
  Table,
  Index,
  Default,
} from 'sequelize-typescript';
import { Student } from './student.model';

@Table({
  tableName: 'student_publications',
  indexes: [
    { fields: ['student_id'] },
    { fields: ['subject'] },
    { fields: ['journal'] },
    { fields: ['publicationDate'] },
    { fields: ['publicationType'] },
  ],
})
export class StudentPublication extends BaseModel {
  @ForeignKey(() => Student)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  student_id!: bigint;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  subject!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  journal!: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: false,
  })
  publicationDate!: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  link?: string;

  // Additional fields for comprehensive publication tracking
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  title!: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  abstract?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: [],
  })
  authors?: string[]; // Array of co-authors

  @Column({
    type: DataType.ENUM(
      'journal_article',
      'conference_paper',
      'book_chapter',
      'book',
      'thesis',
      'dissertation',
      'preprint',
      'patent',
      'other'
    ),
    allowNull: false,
    defaultValue: 'journal_article',
  })
  publicationType!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  doi?: string; // Digital Object Identifier

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  isbn?: string; // For books

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  issn?: string; // For journals

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  volume?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  issue?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  pages?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  publisher?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  conferenceLocation?: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true,
  })
  conferenceDate?: Date;

  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: [],
  })
  keywords?: string[];

  @Column({
    type: DataType.DECIMAL(3, 2),
    allowNull: true,
  })
  impactFactor?: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
  })
  citationCount?: number;

  @Column({
    type: DataType.ENUM('published', 'accepted', 'under_review', 'draft'),
    allowNull: false,
    defaultValue: 'published',
  })
  status!: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  isPeerReviewed!: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  isOpenAccess!: boolean;

  // Document attachments (JSON array of file paths/URLs)
  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: [],
  })
  documents?: string[];

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  notes?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  metadata?: Record<string, any>;

  @BelongsTo(() => Student)
  student!: Student;

  // Virtual properties
  get isPublished(): boolean {
    return this.status === 'published';
  }

  get isRecent(): boolean {
    const fiveYearsAgo = new Date();
    fiveYearsAgo.setFullYear(fiveYearsAgo.getFullYear() - 5);
    return new Date(this.publicationDate) > fiveYearsAgo;
  }

  get authorCount(): number {
    return this.authors ? this.authors.length : 0;
  }

  get hasCoAuthors(): boolean {
    return this.authorCount > 1;
  }

  get publicationYear(): number {
    return new Date(this.publicationDate).getFullYear();
  }

  get fullCitation(): string {
    const authors = this.authors?.join(', ') || 'Unknown';
    const year = this.publicationYear;
    return `${authors} (${year}). ${this.title}. ${this.journal}${this.volume ? `, ${this.volume}` : ''}${this.issue ? `(${this.issue})` : ''}${this.pages ? `, ${this.pages}` : ''}.`;
  }

  get hasDocuments(): boolean {
    return this.documents && this.documents.length > 0;
  }

  get isHighImpact(): boolean {
    return this.impactFactor ? this.impactFactor >= 3.0 : false;
  }

  get isWellCited(): boolean {
    return this.citationCount ? this.citationCount >= 10 : false;
  }
}
