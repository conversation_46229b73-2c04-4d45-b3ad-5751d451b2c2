# Student Database Schema Documentation

This document describes the comprehensive student database schema designed to handle detailed student information as per the provided JSON format.

## Overview

The student database has been restructured into multiple normalized tables to efficiently store and manage student data:

1. **students** - Core student record
2. **student_personal_info** - Personal and contact information
3. **student_academic_records** - Academic history and qualifications
4. **student_proficiency_records** - Language proficiency test results
5. **student_publications** - Research publications and papers
6. **student_social_links** - Social media and professional profiles
7. **student_other_activities** - Certifications, courses, and other activities
8. **emergency_contacts** - Emergency contact information

## Table Relationships

```
students (1) ←→ (1) student_personal_info
students (1) ←→ (n) student_academic_records
students (1) ←→ (n) student_proficiency_records
students (1) ←→ (n) student_publications
students (1) ←→ (n) student_social_links
students (1) ←→ (n) student_other_activities
students (1) ←→ (n) emergency_contacts
```

## Table Details

### 1. students (Core Table)
**Purpose**: Main student record with academic status
**Key Fields**:
- `student_id` (unique identifier)
- `user_id` (link to auth system)
- `academic_level` (freshman, sophomore, etc.)
- `gpa`, `total_credits`
- `status` (active, graduated, etc.)
- `university_id`, `agency_id`

### 2. student_personal_info
**Purpose**: Personal and demographic information
**Key Fields**:
- `firstName`, `lastName`, `nameInNative`
- `email`, `phone`, `guardianPhone`
- `dateOfBirth`, `gender`
- `fatherName`, `motherName`
- `nid`, `passport` (unique identifiers)
- `presentAddress`, `permanentAddress` (JSON)
- `maritalStatus` (JSON with spouse details)
- `sponsor` (JSON with sponsor information)
- `emergencyContact` (JSON with emergency contact)
- `preferredSubject`, `preferredCountry` (JSON arrays)
- `reference`, `note`

### 3. student_academic_records
**Purpose**: Educational background and qualifications
**Key Fields**:
- `foreignDegree` (boolean)
- `nameOfExam`, `institute`, `subject`, `board`
- `grade`, `passingYear`
- `cgpa`, `percentage`, `division`
- `rollNumber`, `registrationNumber`
- `examDate`, `certificateNumber`
- `documents` (JSON array of file paths)

### 4. student_proficiency_records
**Purpose**: Language proficiency test results
**Key Fields**:
- `nameOfExam` (IELTS, TOEFL, etc.)
- `score` (JSON with overall, R, L, W, S scores)
- `examDate`, `expiryDate`
- `testCenter`, `candidateNumber`
- `testType` (academic, general, professional)
- `band`, `level`
- `documents` (JSON array)

### 5. student_publications
**Purpose**: Research publications and academic papers
**Key Fields**:
- `title`, `subject`, `journal`
- `publicationDate`, `link`
- `abstract`, `authors` (JSON array)
- `publicationType` (journal_article, conference_paper, etc.)
- `doi`, `isbn`, `issn`
- `volume`, `issue`, `pages`
- `publisher`, `conferenceLocation`
- `keywords` (JSON array)
- `impactFactor`, `citationCount`
- `status` (published, accepted, under_review)
- `isPeerReviewed`, `isOpenAccess`

### 6. student_social_links
**Purpose**: Social media and professional profiles
**Key Fields**:
- `platform` (linkedin, github, etc.)
- `url`, `username`, `displayName`
- `isPublic`, `isActive`, `isPrimary`
- `isProfessional`
- `description`
- `followerCount`, `followingCount`
- `lastVerified`, `isVerified`

### 7. student_other_activities
**Purpose**: Certifications, courses, workshops, etc.
**Key Fields**:
- `subject`, `title`, `description`
- `certificationLink`
- `startDate`, `endDate`
- `activityType` (certification, course, workshop, etc.)
- `organization`, `instructor`, `location`
- `durationHours`, `certificateNumber`
- `grade`, `score`
- `isCertified`, `isOngoing`
- `certificationExpiryDate`
- `skills`, `technologies` (JSON arrays)
- `cost`, `currency`
- `isSponsored`, `sponsor`

### 8. emergency_contacts (Updated)
**Purpose**: Emergency contact information
**Key Fields**:
- `firstName`, `middleName`, `lastName`
- `relationship`
- `phoneHome`, `phoneMobile`
- `email` (optional)
- `address` (JSON)
- `is_primary`, `can_pick_up`, `can_authorize_medical`

## JSON Data Mapping

### Personal Info JSON → student_personal_info Table
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "nameInNative": "জোন ডো",
  "email": "<EMAIL>",
  "phone": "+**********",
  "guardianPhone": "+**********",
  "dateOfBirth": "1995-05-15",
  "gender": "male",
  "fatherName": "Robert Doe",
  "motherName": "Jane Doe",
  "nid": "**********123456",
  "passport": "*********",
  "presentAddress": {...},
  "permanentAddress": {...},
  "maritalStatus": {...},
  "sponsor": {...},
  "emergencyContact": {...},
  "preferredSubject": [...],
  "preferredCountry": [...],
  "reference": "...",
  "note": "..."
}
```

### Academic Info JSON → Multiple Tables
```json
{
  "academic": [...] → student_academic_records,
  "proficiency": [...] → student_proficiency_records,
  "publications": [...] → student_publications,
  "otherActivities": {...} → student_other_activities
}
```

## Usage Examples

### Creating a Complete Student Record
```typescript
// 1. Create core student record
const student = await Student.create({
  student_id: 'STU001',
  academic_level: 'graduate',
  status: 'active'
});

// 2. Create personal information
await StudentPersonalInfo.create({
  student_id: student.id,
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  // ... other fields
});

// 3. Create academic records
await StudentAcademicRecord.bulkCreate([
  {
    student_id: student.id,
    nameOfExam: 'HSC',
    institute: 'Dhaka College',
    // ... other fields
  }
]);

// 4. Create proficiency records
await StudentProficiencyRecord.create({
  student_id: student.id,
  nameOfExam: 'IELTS',
  score: { overall: 7.5, R: 8.0, L: 7.5, W: 7.0, S: 7.5 },
  // ... other fields
});
```

### Querying Student Data
```typescript
// Get complete student information
const student = await Student.findOne({
  where: { student_id: 'STU001' },
  include: [
    StudentPersonalInfo,
    StudentAcademicRecord,
    StudentProficiencyRecord,
    StudentPublication,
    StudentSocialLink,
    StudentOtherActivity,
    EmergencyContact
  ]
});
```

## Migration

Run the migration to create all tables:
```bash
npm run migration:run
```

To rollback:
```bash
npm run migration:revert
```

## Benefits of This Structure

1. **Normalized Design**: Reduces data redundancy
2. **Scalable**: Easy to add new fields and relationships
3. **Flexible**: Supports complex queries and reporting
4. **Maintainable**: Clear separation of concerns
5. **Performance**: Proper indexing for fast queries
6. **Data Integrity**: Foreign key constraints ensure consistency
