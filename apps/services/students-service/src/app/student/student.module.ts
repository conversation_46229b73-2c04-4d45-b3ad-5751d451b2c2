import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { StudentController } from './student.controller';
import { StudentService } from './student.service';
import { Student } from './models/student.model';
import { Enrollment } from './models/enrollment.model';
import { Grade } from './models/grade.model';
import { EmergencyContact } from './models/emergency-contact.model';
import { StudentAcademicBackground } from './models/student-academic.model';
import { StudentPersonalInfo } from './models/student-personal-info.model';
import { StudentAcademicRecord } from './models/student-academic-record.model';
import { StudentProficiencyRecord } from './models/student-proficiency-record.model';
import { StudentPublication } from './models/student-publication.model';
import { StudentSocialLink } from './models/student-social-link.model';
import { StudentOtherActivity } from './models/student-other-activity.model';
import { StudentDocumentsModule } from './student-documents.module';
import { StudentDocumentsGrpcController } from './controllers/student-documents-grpc.controller';

// Updated to include all student models for proper Sequelize associations
@Module({
  imports: [
    SequelizeModule.forFeature([
      Student,
      Enrollment,
      Grade,
      EmergencyContact,
      StudentAcademicBackground,
      StudentPersonalInfo,
      StudentAcademicRecord,
      StudentProficiencyRecord,
      StudentPublication,
      StudentSocialLink,
      StudentOtherActivity,
    ]),
    StudentDocumentsModule,
  ],
  controllers: [StudentController, StudentDocumentsGrpcController],
  providers: [StudentService],
  exports: [StudentService],
})
export class StudentModule {}
