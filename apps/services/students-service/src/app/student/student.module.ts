import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { StudentController } from './student.controller';
import { StudentService } from './student.service';
import { Student } from './models/student.model';
import { Enrollment } from './models/enrollment.model';
import { Grade } from './models/grade.model';
import { EmergencyContact } from './models/emergency-contact.model';
import { StudentAcademicBackground } from './models/student-academic.model';
import { StudentDocumentsModule } from './student-documents.module';
import { StudentDocumentsGrpcController } from './controllers/student-documents-grpc.controller';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Student,
      Enrollment,
      Grade,
      EmergencyContact,
      StudentAcademicBackground,
    ]),
    StudentDocumentsModule,
  ],
  controllers: [StudentController, StudentDocumentsGrpcController],
  providers: [StudentService],
  exports: [StudentService],
})
export class StudentModule {}
