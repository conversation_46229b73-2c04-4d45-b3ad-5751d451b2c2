import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, WhereOptions } from 'sequelize';
import { Student } from './models/student.model';
import { Enrollment } from './models/enrollment.model';
import { Grade } from './models/grade.model';
import { EmergencyContact } from './models/emergency-contact.model';
import { StudentAcademicBackground } from './models/student-academic.model';
import { StudentPersonalInfo } from './models/student-personal-info.model';

@Injectable()
export class StudentService {
  private readonly logger = new Logger(StudentService.name);

  constructor(
    @InjectModel(Student)
    private readonly studentRepository: typeof Student,
    @InjectModel(Enrollment)
    private readonly enrollmentRepository: typeof Enrollment,
    @InjectModel(Grade)
    private readonly gradeRepository: typeof Grade,
    @InjectModel(EmergencyContact)
    private readonly emergencyContactRepository: typeof EmergencyContact,
    @InjectModel(StudentAcademicBackground)
    private readonly studentAcademicRepository: typeof StudentAcademicBackground,
    @InjectModel(StudentPersonalInfo)
    private readonly studentPersonalInfoRepository: typeof StudentPersonalInfo
  ) {}

  // Student CRUD Operations
  async createStudent(studentData: any): Promise<Student> {
    try {
      // Check if student with student_id already exists
      if (studentData.student_id) {
        const existingStudent = await this.studentRepository.findOne({
          where: { student_id: studentData.student_id },
        });

        if (existingStudent) {
          throw new ConflictException('Student with this student ID already exists');
        }
      }

      // Generate unique student ID
      const studentId = await this.generateStudentId();

      const savedStudent = await this.studentRepository.create({
        ...studentData,
        student_id: studentId,
        enrollment_date: new Date(),
      });

      if (!savedStudent || !savedStudent.id) {
        throw new Error('Failed to create student - no ID returned');
      }

      // Create emergency contacts if provided
      if (
        studentData.emergency_contacts &&
        studentData.emergency_contacts.length > 0
      ) {
        const emergencyContacts = studentData.emergency_contacts.map(
          (contact: any) => ({
            ...contact,
            student_id: savedStudent.id,
          })
        );
        await this.emergencyContactRepository.bulkCreate(emergencyContacts);
      }

      this.logger.log(`Created student: ${savedStudent.id}`);
      return savedStudent;
    } catch (error) {
      this.logger.error('Error creating student:', error);
      throw error;
    }
  }

  async getStudent(id: string): Promise<Student> {
    try {
      const student = await this.studentRepository.findByPk(id, {
        include: [
          { model: this.enrollmentRepository },
          { model: this.gradeRepository },
          { model: this.emergencyContactRepository },
          { model: this.studentPersonalInfoRepository },
        ],
      });

      if (!student) {
        throw new NotFoundException('Student not found');
      }

      return student;
    } catch (error) {
      this.logger.error('Error getting student:', error);
      throw error;
    }
  }

  async updateStudent(id: string, updateData: any): Promise<Student> {
    try {
      const student = await this.getStudent(id);

      // Update student data
      await student.update(updateData);

      // Update emergency contacts if provided
      if (updateData.emergency_contacts) {
        // Remove existing emergency contacts
        await this.emergencyContactRepository.destroy({
          where: { student_id: id },
        });

        // Create new emergency contacts
        const emergencyContacts = updateData.emergency_contacts.map(
          (contact: any) => ({
            ...contact,
            student_id: id,
          })
        );
        await this.emergencyContactRepository.bulkCreate(emergencyContacts);
      }

      this.logger.log(`Updated student: ${id}`);
      return student;
    } catch (error) {
      this.logger.error('Error updating student:', error);
      throw error;
    }
  }

  async deleteStudent(id: string): Promise<void> {
    try {
      const student = await this.getStudent(id);
      await student.destroy();
      this.logger.log(`Deleted student: ${id}`);
    } catch (error) {
      this.logger.error('Error deleting student:', error);
      throw error;
    }
  }

  async listStudents(
    filters: any
  ): Promise<{ students: Student[]; total: number; page_token?: string }> {
    try {
      const {
        page_size = 10,
        page_token = '',
        filter = '',
        order_by = 'created_at desc',
      } = filters;

      const whereCondition: WhereOptions = {};

      // Apply filters
      if (filter) {
        (whereCondition as any)[Op.or] = [
          { student_id: { [Op.iLike]: `%${filter}%` } },
        ];
      }

      // Apply ordering
      const [orderField, orderDirection] = order_by.split(' ');
      const order: any = [[orderField, orderDirection.toUpperCase()]];

      // Apply pagination
      const offset = page_token ? parseInt(page_token, 10) : 0;

      const { rows: students, count: total } =
        await this.studentRepository.findAndCountAll({
          where: whereCondition,
          order,
          limit: page_size,
          offset,
        });

      const nextPageToken =
        offset + page_size < total
          ? (offset + page_size).toString()
          : undefined;

      return {
        students,
        total,
        page_token: nextPageToken,
      };
    } catch (error) {
      this.logger.error('Error listing students:', error);
      throw error;
    }
  }

  // Academic Operations
  async enrollInCourse(
    studentId: string,
    courseId: string,
    semester: string,
    courseData?: any
  ): Promise<Enrollment> {
    try {
      // Check if student exists
      await this.getStudent(studentId);

      // Check if already enrolled in this course for this semester
      const existingEnrollment = await this.enrollmentRepository.findOne({
        where: { student_id: studentId, course_id: courseId, semester },
      });

      if (existingEnrollment) {
        throw new ConflictException(
          'Student is already enrolled in this course for this semester'
        );
      }

      const savedEnrollment = await this.enrollmentRepository.create({
        student_id: studentId,
        course_id: courseId,
        semester,
        enrollment_date: new Date(),
        academic_year: new Date().getFullYear(),
        ...courseData,
      });
      this.logger.log(`Student ${studentId} enrolled in course ${courseId}`);
      return savedEnrollment;
    } catch (error) {
      this.logger.error('Error enrolling in course:', error);
      throw error;
    }
  }

  async dropCourse(
    studentId: string,
    courseId: string,
    semester: string
  ): Promise<void> {
    try {
      const enrollment = await this.enrollmentRepository.findOne({
        where: { student_id: studentId, course_id: courseId, semester },
      });

      if (!enrollment) {
        throw new NotFoundException('Enrollment not found');
      }

      await enrollment.update({
        status: 'dropped',
        drop_date: new Date(),
      });

      this.logger.log(`Student ${studentId} dropped course ${courseId}`);
    } catch (error) {
      this.logger.error('Error dropping course:', error);
      throw error;
    }
  }

  async getEnrollments(
    studentId: string,
    semester?: string
  ): Promise<Enrollment[]> {
    try {
      const whereCondition: any = { student_id: studentId };
      if (semester) {
        whereCondition.semester = semester;
      }

      const enrollments = await this.enrollmentRepository.findAll({
        where: whereCondition,
        include: [{ model: this.gradeRepository }],
        order: [['createdAt', 'DESC']],
      });

      return enrollments;
    } catch (error) {
      this.logger.error('Error getting enrollments:', error);
      throw error;
    }
  }

  async updateGrades(
    studentId: string,
    courseId: string,
    gradeData: any
  ): Promise<Grade> {
    try {
      // Find the enrollment
      const enrollment = await this.enrollmentRepository.findOne({
        where: { student_id: studentId, course_id: courseId },
      });

      if (!enrollment) {
        throw new NotFoundException('Enrollment not found');
      }

      // Check if grade already exists
      let grade = await this.gradeRepository.findOne({
        where: { student_id: studentId, enrollment_id: enrollment.id },
      });

      if (grade) {
        // Update existing grade
        await grade.update(gradeData);
      } else {
        // Create new grade
        grade = await this.gradeRepository.create({
          student_id: studentId,
          enrollment_id: enrollment.id,
          course_id: courseId,
          grade_date: new Date(),
          academic_year: enrollment.academic_year,
          semester: enrollment.semester,
          credits: enrollment.credits,
          course_name: enrollment.course_name,
          course_code: enrollment.course_code,
          ...gradeData,
        });
      }

      // Update student GPA
      await this.updateStudentGPA(studentId);

      this.logger.log(
        `Updated grade for student ${studentId} in course ${courseId}`
      );
      return grade;
    } catch (error) {
      this.logger.error('Error updating grades:', error);
      throw error;
    }
  }

  async getAcademicProgress(studentId: string): Promise<any> {
    try {
      const student = await this.getStudent(studentId);
      const enrollments = await this.getEnrollments(studentId);
      const grades = await this.gradeRepository.findAll({
        where: { student_id: studentId },
        order: [
          ['academic_year', 'DESC'],
          ['semester', 'DESC'],
        ],
      });

      // Calculate academic statistics
      const totalCreditsAttempted = grades.reduce(
        (sum, grade) => sum + grade.credits,
        0
      );
      const totalQualityPoints = grades.reduce(
        (sum, grade) => sum + grade.quality_points,
        0
      );
      const currentGPA =
        totalCreditsAttempted > 0
          ? totalQualityPoints / totalCreditsAttempted
          : 0;

      const completedCourses = grades.filter(
        (grade) => grade.is_passing
      ).length;
      const failedCourses = grades.filter((grade) => !grade.is_passing).length;

      return {
        student_id: studentId,
        student_name: student.full_name,
        current_gpa: Math.round(currentGPA * 100) / 100,
        total_credits_attempted: totalCreditsAttempted,
        total_credits_earned: grades
          .filter((g) => g.is_passing)
          .reduce((sum, g) => sum + g.credits, 0),
        completed_courses: completedCourses,
        failed_courses: failedCourses,
        current_enrollments: enrollments.filter((e) => e.is_current).length,
        academic_standing: this.getAcademicStanding(currentGPA),
        semester_breakdown: this.getSemesterBreakdown(grades),
      };
    } catch (error) {
      this.logger.error('Error getting academic progress:', error);
      throw error;
    }
  }

  async getTranscript(studentId: string): Promise<any> {
    try {
      const student = await this.getStudent(studentId);
      const grades = await this.gradeRepository.findAll({
        where: { student_id: studentId },
        order: [
          ['academic_year', 'ASC'],
          ['semester', 'ASC'],
        ],
      });

      const semesterGroups = this.groupGradesBySemester(grades);

      return {
        student_info: {
          id: student.id,
          student_id: student.student_id,
          name: student.full_name,
          email: student.personalInfo?.email || '',
          major: 'Not specified', // This field was moved to academic records
          minor: 'Not specified', // This field was moved to academic records
          enrollment_date: student.enrollment_date,
          graduation_date: student.graduation_date,
          status: student.status,
        },
        academic_summary: {
          cumulative_gpa: student.gpa,
          total_credits: student.total_credits,
          completed_credits: student.total_credits, // Using total_credits as completed_credits
          academic_standing: this.getAcademicStanding(student.gpa || 0),
        },
        semester_records: semesterGroups,
        generated_at: new Date(),
      };
    } catch (error) {
      this.logger.error('Error getting transcript:', error);
      throw error;
    }
  }

  // Helper methods
  private async generateStudentId(): Promise<string> {
    const year = new Date().getFullYear().toString().slice(-2);
    const randomNum = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');
    return `ST${year}${randomNum}`;
  }

  private async updateStudentGPA(studentId: string): Promise<void> {
    const grades = await this.gradeRepository.findAll({
      where: { student_id: studentId },
    });

    if (grades.length === 0) return;

    const totalCredits = grades.reduce((sum, grade) => sum + grade.credits, 0);
    const totalQualityPoints = grades.reduce(
      (sum, grade) => sum + grade.quality_points,
      0
    );
    const gpa = totalCredits > 0 ? totalQualityPoints / totalCredits : 0;

    const completedCredits = grades
      .filter((g) => g.is_passing)
      .reduce((sum, g) => sum + g.credits, 0);

    await this.studentRepository.update(
      {
        gpa: Math.round(gpa * 100) / 100,
        total_credits: completedCredits,
      },
      {
        where: { id: studentId },
      }
    );
  }

  private getAcademicStanding(gpa: number): string {
    if (gpa >= 3.8) return 'Summa Cum Laude';
    if (gpa >= 3.6) return 'Magna Cum Laude';
    if (gpa >= 3.4) return 'Cum Laude';
    if (gpa >= 3.0) return 'Good Standing';
    if (gpa >= 2.0) return 'Satisfactory';
    if (gpa >= 1.0) return 'Probation';
    return 'Academic Warning';
  }

  private getSemesterBreakdown(grades: Grade[]): any[] {
    const semesterMap = new Map();

    grades.forEach((grade) => {
      const key = `${grade.academic_year}-${grade.semester}`;
      if (!semesterMap.has(key)) {
        semesterMap.set(key, {
          academic_year: grade.academic_year,
          semester: grade.semester,
          courses: [],
          total_credits: 0,
          semester_gpa: 0,
        });
      }

      const semesterData = semesterMap.get(key);
      semesterData.courses.push(grade);
      semesterData.total_credits += grade.credits;
    });

    // Calculate semester GPAs
    semesterMap.forEach((semesterData) => {
      const totalQualityPoints = semesterData.courses.reduce(
        (sum: number, grade: Grade) => sum + grade.quality_points,
        0
      );
      semesterData.semester_gpa =
        semesterData.total_credits > 0
          ? Math.round(
              (totalQualityPoints / semesterData.total_credits) * 100
            ) / 100
          : 0;
    });

    return Array.from(semesterMap.values()).sort((a, b) => {
      if (a.academic_year !== b.academic_year) {
        return a.academic_year - b.academic_year;
      }
      return a.semester.localeCompare(b.semester);
    });
  }

  private groupGradesBySemester(grades: Grade[]): any[] {
    return this.getSemesterBreakdown(grades);
  }

  // Academic Background Operations
  async createOrUpdateStudentAcademic(
    academicData: any
  ): Promise<StudentAcademicBackground> {
    try {
      this.logger.log(
        'Creating/updating academic background for student:',
        academicData.student_id
      );

      // Check if student exists
      const student = await this.studentRepository.findByPk(
        academicData.student_id
      );
      if (!student) {
        throw new NotFoundException('Student not found');
      }

      // Check if academic background already exists
      let academicBackground = await this.studentAcademicRepository.findOne({
        where: { student_id: academicData.student_id },
      });

      if (academicBackground) {
        // Update existing record
        await academicBackground.update({
          academic_records: academicData.academic || [],
          proficiency_records: academicData.proficiency || [],
          publication_records: academicData.publications || [],
          other_activities: academicData.other_activities || {},
        });
      } else {
        // Create new record
        academicBackground = await this.studentAcademicRepository.create({
          student_id: academicData.student_id,
          academic_records: academicData.academic || [],
          proficiency_records: academicData.proficiency || [],
          publication_records: academicData.publications || [],
          other_activities: academicData.other_activities || {},
        });
      }

      return academicBackground;
    } catch (error) {
      this.logger.error(
        'Error creating/updating student academic background:',
        error
      );
      throw error;
    }
  }

  async getStudentAcademic(
    studentId: string
  ): Promise<StudentAcademicBackground> {
    try {
      this.logger.log('Getting academic background for student:', studentId);

      // Check if student exists
      const student = await this.studentRepository.findByPk(studentId);
      if (!student) {
        throw new NotFoundException('Student not found');
      }

      const academicBackground = await this.studentAcademicRepository.findOne({
        where: { student_id: studentId },
        include: [
          {
            model: Student,
            attributes: ['id', 'student_id'],
          },
        ],
      });

      if (!academicBackground) {
        throw new NotFoundException(
          'Academic background not found for this student'
        );
      }

      return academicBackground;
    } catch (error) {
      this.logger.error('Error getting student academic background:', error);
      throw error;
    }
  }
}
