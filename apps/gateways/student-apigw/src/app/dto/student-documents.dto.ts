import {
  IsString,
  IsBoolean,
  IsArray,
  IsOptional,
  IsNumber,
  ValidateNested,
  ArrayMinSize,
  IsNotEmpty,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class DependentDto {
  @ApiProperty({ description: 'Dependent name' })
  @IsString()
  @IsNotEmpty()
  name!: string;

  @ApiProperty({ description: 'Dependent passport number' })
  @IsString()
  @IsNotEmpty()
  passport!: string;
}

export class ChildDto {
  @ApiProperty({ description: 'Child name' })
  @IsString()
  @IsNotEmpty()
  name!: string;

  @ApiProperty({ description: 'Child passport number' })
  @IsString()
  @IsNotEmpty()
  passport!: string;
}

export class SponsorMetadataDto {
  @ApiProperty({ description: 'Sponsor name' })
  @IsString()
  @IsNotEmpty()
  name!: string;

  @ApiPropertyOptional({ description: 'Sponsor relationship' })
  @IsString()
  @IsOptional()
  relationship?: string;

  @ApiPropertyOptional({ description: 'Sponsor phone number' })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiPropertyOptional({ description: 'Sponsor email' })
  @IsString()
  @IsOptional()
  email?: string;
}

export class CreateStudentDocumentsDto {
  @ApiProperty({ description: 'Student ID from route parameter' })
  @IsNumber()
  @Type(() => Number)
  studentId!: number;

  @ApiProperty({ description: 'Sponsor name' })
  @IsString()
  @IsNotEmpty()
  sponsorName!: string;

  @ApiProperty({ description: 'Whether student has dependents' })
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return Boolean(value);
  })
  takeDependents!: boolean;

  @ApiProperty({ 
    description: 'Academic sections to process',
    type: [String],
    example: ['ssc', 'hsc', 'bachelor']
  })
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(0)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return [value];
      }
    }
    return Array.isArray(value) ? value : [];
  })
  academicSections!: string[];

  @ApiProperty({ 
    description: 'Proficiency test sections to process',
    type: [String],
    example: ['ielts', 'duolingo']
  })
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(0)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return [value];
      }
    }
    return Array.isArray(value) ? value : [];
  })
  proficiencySections!: string[];

  @ApiPropertyOptional({ 
    description: 'Sponsor metadata',
    type: SponsorMetadataDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SponsorMetadataDto)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return null;
      }
    }
    return value;
  })
  sponsorMetadata?: SponsorMetadataDto;

  @ApiPropertyOptional({ 
    description: 'List of dependents',
    type: [DependentDto]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DependentDto)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return [];
      }
    }
    return Array.isArray(value) ? value : [];
  })
  dependents?: DependentDto[];

  @ApiPropertyOptional({ 
    description: 'List of children',
    type: [ChildDto]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChildDto)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return [];
      }
    }
    return Array.isArray(value) ? value : [];
  })
  children?: ChildDto[];
}

export class StudentDocumentResponseDto {
  @ApiProperty({ description: 'Document ID' })
  id!: number;

  @ApiProperty({ description: 'Student ID' })
  student_id!: number;

  @ApiProperty({ description: 'Document section' })
  section!: string;

  @ApiProperty({ description: 'Document field name' })
  field!: string;

  @ApiProperty({ description: 'Generated filename' })
  filename!: string;

  @ApiProperty({ description: 'File URL' })
  url!: string;

  @ApiProperty({ description: 'MIME type' })
  mimeType!: string;

  @ApiProperty({ description: 'File size in bytes' })
  fileSize!: number;

  @ApiProperty({ description: 'Original filename' })
  originalName!: string;

  @ApiProperty({ description: 'MinIO bucket' })
  bucket!: string;

  @ApiProperty({ description: 'MinIO object key' })
  objectKey!: string;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  metadata?: any;

  @ApiProperty({ description: 'Creation timestamp' })
  created_at!: Date;

  @ApiProperty({ description: 'Update timestamp' })
  updated_at!: Date;
}

export class CreateStudentDocumentsResponseDto {
  @ApiProperty({ description: 'Success status' })
  success!: boolean;

  @ApiProperty({ description: 'Response message' })
  message!: string;

  @ApiProperty({ 
    description: 'Uploaded documents',
    type: [StudentDocumentResponseDto]
  })
  documents!: StudentDocumentResponseDto[];

  @ApiProperty({ description: 'Total number of files uploaded' })
  totalUploaded!: number;

  @ApiProperty({ description: 'Number of files that failed to upload' })
  failedUploads!: number;

  @ApiPropertyOptional({ description: 'Error details for failed uploads' })
  errors?: string[];
}

// DTO for querying student documents
export class GetStudentDocumentsQueryDto {
  @ApiPropertyOptional({ description: 'Filter by section' })
  @IsOptional()
  @IsString()
  section?: string;

  @ApiPropertyOptional({ description: 'Filter by field' })
  @IsOptional()
  @IsString()
  field?: string;

  @ApiPropertyOptional({ description: 'Filter by verification status' })
  @IsOptional()
  @IsString()
  verificationStatus?: 'pending' | 'verified' | 'rejected' | 'expired';

  @ApiPropertyOptional({ description: 'Include inactive documents' })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return Boolean(value);
  })
  includeInactive?: boolean;
}

// DTO for updating document verification status
export class UpdateDocumentVerificationDto {
  @ApiProperty({ description: 'Verification status' })
  @IsString()
  @IsNotEmpty()
  verificationStatus!: 'pending' | 'verified' | 'rejected' | 'expired';

  @ApiPropertyOptional({ description: 'Verification notes' })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({ description: 'Document expiry date' })
  @IsOptional()
  @Type(() => Date)
  expiryDate?: Date;
}
