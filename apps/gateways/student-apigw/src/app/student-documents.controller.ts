import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseInterceptors,
  UploadedFiles,
  ParseIntPipe,
  ValidationPipe,
  UsePipes,
  Logger,
  BadRequestException,
  UseGuards,
} from '@nestjs/common';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { MulterFile } from '@apply-goal-backend/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiConsumes,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { StudentDocumentsClientService } from './student-documents.service';
import {
  CreateStudentDocumentsDto,
  CreateStudentDocumentsResponseDto,
  GetStudentDocumentsQueryDto,
  UpdateDocumentVerificationDto,
  StudentDocumentResponseDto,
} from './dto/student-documents.dto';
import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';
import { JwtAuthGuard } from '@apply-goal-backend/auth';

@ApiTags('Student Documents')
@Controller('students/:studentId/documents')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
export class StudentDocumentsController {
  private readonly logger = new Logger(StudentDocumentsController.name);

  constructor(private readonly studentDocumentsService: StudentDocumentsClientService) {}

  @Post()
  @ApiOperation({ 
    summary: 'Upload student documents',
    description: 'Upload multiple documents for a student using multipart form data'
  })
  @ApiParam({ name: 'studentId', description: 'Student ID', type: 'number' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Multipart form data with files and metadata',
    schema: {
      type: 'object',
      properties: {
        sponsorName: { type: 'string', example: 'Philip Carter' },
        takeDependents: { type: 'boolean', example: true },
        academicSections: { 
          type: 'string', 
          example: '["ssc","hsc","bachelor"]',
          description: 'JSON array of academic sections'
        },
        proficiencySections: { 
          type: 'string', 
          example: '["duolingo","ielts"]',
          description: 'JSON array of proficiency sections'
        },
        dependents: {
          type: 'string',
          example: '[{"name":"John Grey","passport":"X1234567"}]',
          description: 'JSON array of dependents'
        },
        children: {
          type: 'string',
          example: '[{"name":"Jane Grey","passport":"Y7654321"}]',
          description: 'JSON array of children'
        },
        // File fields
        photo: { type: 'string', format: 'binary' },
        signature: { type: 'string', format: 'binary' },
        ssc: { type: 'string', format: 'binary' },
        hsc: { type: 'string', format: 'binary' },
        bachelor: { type: 'string', format: 'binary' },
        duolingo: { type: 'string', format: 'binary' },
        ielts: { type: 'string', format: 'binary' },
        sponsorPhoto: { type: 'string', format: 'binary' },
        sponsorBankStatement: { type: 'string', format: 'binary' },
        'dependents[0][photo]': { type: 'string', format: 'binary' },
        'dependents[0][passport]': { type: 'string', format: 'binary' },
        'children[0][photo]': { type: 'string', format: 'binary' },
        'children[0][passport]': { type: 'string', format: 'binary' },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Documents uploaded successfully',
    type: CreateStudentDocumentsResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request - validation failed' })
  @ApiResponse({ status: 404, description: 'Student not found' })
  @UseInterceptors(AnyFilesInterceptor())
  async uploadDocuments(
    @Param('studentId', ParseIntPipe) studentId: number,
    @UploadedFiles() files: MulterFile[],
    @Body() body: any
  ): Promise<CreateStudentDocumentsResponseDto> {
    this.logger.log(`API Gateway: Uploading documents for student ${studentId}`);
    this.logger.debug(`Received ${files?.length || 0} files`);
    this.logger.debug(`Body keys: ${Object.keys(body).join(', ')}`);

    // Convert files array to object with fieldnames as keys
    const filesObject: { [fieldname: string]: MulterFile[] } = {};
    if (files) {
      for (const file of files) {
        if (!filesObject[file.fieldname]) {
          filesObject[file.fieldname] = [];
        }
        filesObject[file.fieldname].push(file);
      }
    }

    // Create and validate DTO
    const dto = plainToClass(CreateStudentDocumentsDto, {
      studentId,
      ...body,
    });

    // Manual validation since we're dealing with multipart form data
    const errors = await validate(dto);
    if (errors.length > 0) {
      const errorMessages = errors.map(error => 
        Object.values(error.constraints || {}).join(', ')
      ).join('; ');
      throw new BadRequestException(`Validation failed: ${errorMessages}`);
    }

    return await this.studentDocumentsService.uploadDocuments(studentId, dto, filesObject);
  }

  @Get()
  @ApiOperation({ 
    summary: 'Get student documents',
    description: 'Retrieve all documents for a student with optional filtering'
  })
  @ApiParam({ name: 'studentId', description: 'Student ID', type: 'number' })
  @ApiQuery({ name: 'section', required: false, description: 'Filter by section' })
  @ApiQuery({ name: 'field', required: false, description: 'Filter by field' })
  @ApiQuery({ name: 'verificationStatus', required: false, description: 'Filter by verification status' })
  @ApiQuery({ name: 'includeInactive', required: false, type: 'boolean', description: 'Include inactive documents' })
  @ApiResponse({
    status: 200,
    description: 'Documents retrieved successfully',
    type: [StudentDocumentResponseDto],
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async getDocuments(
    @Param('studentId', ParseIntPipe) studentId: number,
    @Query() query: GetStudentDocumentsQueryDto
  ): Promise<StudentDocumentResponseDto[]> {
    this.logger.log(`API Gateway: Getting documents for student ${studentId}`);
    return await this.studentDocumentsService.getStudentDocuments(studentId, query);
  }

  @Get(':documentId')
  @ApiOperation({ 
    summary: 'Get specific document',
    description: 'Retrieve a specific document by ID'
  })
  @ApiParam({ name: 'studentId', description: 'Student ID', type: 'number' })
  @ApiParam({ name: 'documentId', description: 'Document ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Document retrieved successfully',
    type: StudentDocumentResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Document not found' })
  async getDocument(
    @Param('studentId', ParseIntPipe) studentId: number,
    @Param('documentId', ParseIntPipe) documentId: number
  ): Promise<StudentDocumentResponseDto> {
    this.logger.log(`API Gateway: Getting document ${documentId} for student ${studentId}`);
    return await this.studentDocumentsService.getDocument(studentId, documentId);
  }

  @Put(':documentId/verification')
  @ApiOperation({ 
    summary: 'Update document verification status',
    description: 'Update the verification status of a document'
  })
  @ApiParam({ name: 'studentId', description: 'Student ID', type: 'number' })
  @ApiParam({ name: 'documentId', description: 'Document ID', type: 'number' })
  @ApiBody({ type: UpdateDocumentVerificationDto })
  @ApiResponse({
    status: 200,
    description: 'Document verification updated successfully',
    type: StudentDocumentResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Document not found' })
  @UsePipes(new ValidationPipe({ transform: true }))
  async updateDocumentVerification(
    @Param('studentId', ParseIntPipe) studentId: number,
    @Param('documentId', ParseIntPipe) documentId: number,
    @Body() updateDto: UpdateDocumentVerificationDto
  ): Promise<StudentDocumentResponseDto> {
    this.logger.log(`API Gateway: Updating verification for document ${documentId} of student ${studentId}`);
    return await this.studentDocumentsService.updateDocumentVerification(documentId, updateDto);
  }

  @Delete(':documentId')
  @ApiOperation({ 
    summary: 'Delete document',
    description: 'Soft delete a document (sets isActive to false)'
  })
  @ApiParam({ name: 'studentId', description: 'Student ID', type: 'number' })
  @ApiParam({ name: 'documentId', description: 'Document ID', type: 'number' })
  @ApiResponse({ status: 200, description: 'Document deleted successfully' })
  @ApiResponse({ status: 404, description: 'Document not found' })
  async deleteDocument(
    @Param('studentId', ParseIntPipe) studentId: number,
    @Param('documentId', ParseIntPipe) documentId: number
  ): Promise<{ message: string }> {
    this.logger.log(`API Gateway: Deleting document ${documentId} for student ${studentId}`);
    return await this.studentDocumentsService.deleteDocument(studentId, documentId);
  }

  @Get('sections/summary')
  @ApiOperation({ 
    summary: 'Get document sections summary',
    description: 'Get a summary of documents grouped by section'
  })
  @ApiParam({ name: 'studentId', description: 'Student ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Document sections summary',
    schema: {
      type: 'object',
      properties: {
        sections: {
          type: 'object',
          additionalProperties: {
            type: 'object',
            properties: {
              total: { type: 'number' },
              verified: { type: 'number' },
              pending: { type: 'number' },
              rejected: { type: 'number' },
              fields: { type: 'array', items: { type: 'string' } }
            }
          }
        }
      }
    }
  })
  async getDocumentsSummary(
    @Param('studentId', ParseIntPipe) studentId: number
  ): Promise<any> {
    this.logger.log(`API Gateway: Getting documents summary for student ${studentId}`);
    return await this.studentDocumentsService.getDocumentsSummary(studentId);
  }
}
