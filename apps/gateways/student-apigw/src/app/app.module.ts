import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { StudentController } from './student.controller';
import { StudentClientService } from './student.service';
import { StudentDocumentsController } from './student-documents.controller';
import { StudentDocumentsClientService } from './student-documents.service';
import { MonitoringModule } from '@apply-goal-backend/monitoring';
import { MetricsController } from './metrics.controller';
import { MetricsMiddleware } from './middleware/metrics.middleware';
import { AuthenticationModule } from '@apply-goal-backend/auth';

@Module({
  imports: [
    ClientsModule.register([
      {
        name: 'STUDENTS_SERVICE',
        transport: Transport.GRPC,
        options: {
          package: ['student_documents'],
          protoPath: [
            join(__dirname, '../../../libs/shared/dto/src/lib/students/student-documents.proto'),
          ],
          url: process.env.STUDENTS_SERVICE_URL || 'students-service:50053',
        },
      },
    ]),
    MonitoringModule.forRoot({
      metrics: {
        serviceName: 'student-apigw',
        serviceVersion: '1.0.0',
        port: parseInt(process.env.PORT || '4007', 10), // Use the same port as the app
        path: '/api/metrics',
        labels: {
          environment: process.env.NODE_ENV || 'development',
          service_type: 'gateway',
        },
      },
      tracing: {
        serviceName: 'student-apigw',
        jaegerEndpoint: 'http://jaeger:4318/v1/traces',
      },
    }),
    AuthenticationModule.forRoot({
      secret: process.env.JWT_SECRET || 'secret',
      expiresIn: '1h',
      refreshExpiresIn: '7d',
    }),
  ],
  controllers: [AppController, StudentController, StudentDocumentsController, MetricsController],
  providers: [AppService, StudentClientService, StudentDocumentsClientService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(MetricsMiddleware)
      .exclude('/api/metrics') // Exclude metrics endpoint to avoid recursive tracking
      .forRoutes('*');
  }
}
