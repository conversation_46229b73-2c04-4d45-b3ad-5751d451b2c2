#!/bin/bash

# Test script for Student API Gateway permissions
# This script helps verify that the student APIs are working with proper authentication

BASE_URL="http://localhost:4007/api"
STUDENT_API_URL="$BASE_URL/student"

echo "🧪 Testing Student API Gateway Permissions"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local token=$3
    local data=$4
    local description=$5
    
    echo -e "\n${YELLOW}Testing: $description${NC}"
    echo "Endpoint: $method $endpoint"
    
    if [ -n "$token" ]; then
        echo "Using token: ${token:0:20}..."
    else
        echo "No token provided"
    fi
    
    # Build curl command
    local curl_cmd="curl -s -w '\nHTTP Status: %{http_code}\n'"
    
    if [ -n "$token" ]; then
        curl_cmd="$curl_cmd -H 'Authorization: Bearer $token'"
    fi
    
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
    fi
    
    curl_cmd="$curl_cmd -X $method '$STUDENT_API_URL$endpoint'"
    
    # Execute and capture result
    local result=$(eval $curl_cmd)
    local status=$(echo "$result" | grep "HTTP Status:" | cut -d' ' -f3)
    local response=$(echo "$result" | sed '/HTTP Status:/d')
    
    if [ "$status" = "200" ] || [ "$status" = "201" ]; then
        echo -e "${GREEN}✅ SUCCESS (Status: $status)${NC}"
        echo "Response: $response" | head -c 200
        echo "..."
    elif [ "$status" = "401" ]; then
        echo -e "${RED}❌ UNAUTHORIZED (Status: $status)${NC}"
        echo "Response: $response"
    elif [ "$status" = "403" ]; then
        echo -e "${RED}❌ FORBIDDEN (Status: $status)${NC}"
        echo "Response: $response"
    else
        echo -e "${YELLOW}⚠️  OTHER (Status: $status)${NC}"
        echo "Response: $response"
    fi
}

# Test data
STUDENT_DATA='{
  "firstName": "John",
  "lastName": "Doe",
  "nameInNative": "জোন ডো",
  "email": "<EMAIL>",
  "phone": "+*********0",
  "guardianPhone": "+*********1",
  "dateOfBirth": "1995-01-15",
  "gender": "male",
  "fatherName": "Robert Doe",
  "motherName": "Jane Doe",
  "nid": "*********",
  "passport": "A12345678",
  "presentAddress": {
    "address": "123 Main St",
    "country": "USA",
    "state": "CA",
    "city": "Los Angeles",
    "postalCode": "90210"
  },
  "permanentAddress": {
    "address": "456 Oak Ave",
    "country": "USA",
    "state": "NY",
    "city": "New York",
    "postalCode": "10001"
  },
  "maritalStatus": {
    "status": "single",
    "spouseName": "",
    "spousePhone": "",
    "spousePassport": ""
  },
  "sponsor": {
    "name": "Robert Doe",
    "relation": "father",
    "phone": "+*********1",
    "email": "<EMAIL>"
  },
  "emergencyContact": {
    "lastName": "Smith",
    "middleName": "",
    "firstName": "Emergency",
    "phoneHome": "+*********2",
    "phoneMobile": "+*********3",
    "relation": "friend"
  },
  "preferredSubject": ["Computer Science", "Mathematics"],
  "preferredCountry": ["USA", "Canada"],
  "socialLinks": [
    {
      "platform": "linkedin",
      "url": "https://linkedin.com/in/johndoe"
    }
  ],
  "reference": "Academic reference from previous institution",
  "note": "Student interested in graduate studies"
}'

echo "📋 Test Cases:"
echo "1. Public student registration (should work without token)"
echo "2. Create student with SPM_CREATE permission"
echo "3. Get student with SPM_VIEW permission"
echo "4. Update student with SPM_EDIT permission"
echo "5. List students with SPM_VIEW permission"
echo "6. Delete student with SPM_DELETE permission"

# Test 1: Public registration (no token)
test_endpoint "POST" "/register" "" "$STUDENT_DATA" "Public Student Registration"

# Test 2: Create student (requires SPM_CREATE permission)
test_endpoint "POST" "" "YOUR_JWT_TOKEN_HERE" "$STUDENT_DATA" "Create Student (SPM_CREATE)"

# Test 3: Get student (requires SPM_VIEW permission)
test_endpoint "GET" "/1" "YOUR_JWT_TOKEN_HERE" "" "Get Student (SPM_VIEW)"

# Test 4: Update student (requires SPM_EDIT permission)
test_endpoint "PUT" "/1" "YOUR_JWT_TOKEN_HERE" "$STUDENT_DATA" "Update Student (SPM_EDIT)"

# Test 5: List students (requires SPM_VIEW permission)
test_endpoint "GET" "" "YOUR_JWT_TOKEN_HERE" "" "List Students (SPM_VIEW)"

# Test 6: Delete student (requires SPM_DELETE permission)
test_endpoint "DELETE" "/1" "YOUR_JWT_TOKEN_HERE" "" "Delete Student (SPM_DELETE)"

echo -e "\n${YELLOW}📝 Instructions:${NC}"
echo "1. Replace 'YOUR_JWT_TOKEN_HERE' with a valid JWT token"
echo "2. Make sure the user has the required permissions:"
echo "   - SPM_CREATE for creating students"
echo "   - SPM_VIEW for viewing students"
echo "   - SPM_EDIT for updating students"
echo "   - SPM_DELETE for deleting students"
echo "3. The public registration endpoint should work without authentication"
echo "4. All other endpoints require proper JWT authentication and permissions"

echo -e "\n${GREEN}✅ Test script completed!${NC}" 