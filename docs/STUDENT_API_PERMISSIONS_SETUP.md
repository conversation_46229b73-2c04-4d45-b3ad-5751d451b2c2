# Student API Permissions Setup Guide

## Overview

The Student API Gateway requires specific permissions for different operations. This guide explains how to set up permissions to resolve 401 unauthorized errors.

## Problem

All student endpoints were previously using `SPM_VIEW` permission, which caused 401 unauthorized errors for users who didn't have this specific permission.

## Solution

We've implemented granular permissions for different student operations:

### Available Permissions

```typescript
// Student Profile Management Permissions
SPM_VIEW = 'StudentProfileManagement:View'      // View student profiles
SPM_CREATE = 'StudentProfileManagement:Create'  // Create student profiles
SPM_EDIT = 'StudentProfileManagement:Edit'      // Edit student profiles
SPM_DELETE = 'StudentProfileManagement:Delete'  // Delete student profiles
SPM_COMMUNICATION_LOGS = 'StudentProfileManagement:Communicationlogs'
```

### Endpoint Permissions Mapping

| Endpoint | Method | Permission Required | Description |
|----------|--------|-------------------|-------------|
| `/api/student/register` | POST | `@Public()` | Public student registration (no auth) |
| `/api/student` | POST | `SPM_CREATE` | Create student personal info |
| `/api/student/:id` | PUT | `SPM_EDIT` | Update student personal info |
| `/api/student/:id` | GET | `SPM_VIEW` | Get student by ID |
| `/api/student` | GET | `SPM_VIEW` | List students |
| `/api/student/:id` | DELETE | `SPM_DELETE` | Delete student |
| `/api/student/legacy` | POST | `SPM_CREATE` | Legacy create student |
| `/api/student/legacy/:id` | PUT | `SPM_EDIT` | Legacy update student |
| `/api/student/:studentId/enrollments` | POST | `SPM_EDIT` | Enroll in course |
| `/api/student/:studentId/enrollments/:courseId` | DELETE | `SPM_EDIT` | Drop course |
| `/api/student/:studentId/enrollments` | GET | `SPM_VIEW` | Get enrollments |
| `/api/student/:studentId/grades` | PUT | `SPM_EDIT` | Update grades |
| `/api/student/:studentId/academic-progress` | GET | `SPM_VIEW` | Get academic progress |
| `/api/student/:studentId/transcript` | GET | `SPM_VIEW` | Get transcript |
| `/api/student/:id/academic` | POST | `SPM_EDIT` | Create/update academic info |
| `/api/student/:id/academic` | GET | `SPM_VIEW` | Get academic info |

## Setting Up User Permissions

### 1. Assign Permissions to Users

Users need to have the appropriate permissions assigned to their accounts. This is typically done through the auth service or admin panel.

### 2. JWT Token Requirements

The JWT token must include the required permissions in the payload:

```json
{
  "sub": 123,
  "email": "<EMAIL>",
  "roles": ["Student", "Counselor"],
  "permissions": [
    "StudentProfileManagement:View",
    "StudentProfileManagement:Create",
    "StudentProfileManagement:Edit"
  ],
  "organizationId": "org-123",
  "type": "access"
}
```

### 3. Testing Permissions

Use the provided test script to verify permissions:

```bash
# Make the script executable
chmod +x scripts/test-student-permissions.sh

# Run the test script
./scripts/test-student-permissions.sh
```

## Common Issues and Solutions

### Issue 1: 401 Unauthorized
**Cause**: Missing or invalid JWT token
**Solution**: 
- Ensure the request includes a valid `Authorization: Bearer <token>` header
- Verify the token hasn't expired
- Check that the token is properly formatted

### Issue 2: 403 Forbidden
**Cause**: User lacks required permissions
**Solution**:
- Assign the required permission to the user's account
- Check that the permission is included in the JWT token payload
- Verify the permission string matches exactly (case-sensitive)

### Issue 3: Student Create API Works but Others Don't
**Cause**: User has `SPM_CREATE` but not `SPM_VIEW` permission
**Solution**:
- Assign `SPM_VIEW` permission to the user
- Or use the public registration endpoint: `POST /api/student/register`

## Public Registration Endpoint

For student self-registration, use the public endpoint:

```bash
curl -X POST http://localhost:4007/api/student/register \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    ...
  }'
```

This endpoint doesn't require authentication and automatically creates a user account.

## Role-Based Access

Consider assigning permissions based on user roles:

### Student Role
- `SPM_VIEW` - View own profile
- `SPM_EDIT` - Edit own profile

### Counselor Role
- `SPM_VIEW` - View student profiles
- `SPM_CREATE` - Create student profiles
- `SPM_EDIT` - Edit student profiles

### Admin Role
- `SPM_VIEW` - View all student profiles
- `SPM_CREATE` - Create student profiles
- `SPM_EDIT` - Edit student profiles
- `SPM_DELETE` - Delete student profiles

## Monitoring and Debugging

### Check User Permissions
```bash
# Decode JWT token to check permissions
echo "YOUR_JWT_TOKEN" | base64 -d | jq .
```

### Logs to Check
- Student API Gateway logs: `docker compose logs student-apigw`
- Auth service logs: `docker compose logs auth-service`
- Check for permission-related errors in the logs

### Health Check
```bash
# Check if services are running
curl http://localhost:4007/health
```

## Migration Guide

If you're upgrading from the old permission system:

1. **Update User Permissions**: Assign the new granular permissions to existing users
2. **Test Endpoints**: Use the test script to verify all endpoints work
3. **Update Frontend**: Ensure frontend sends proper authentication headers
4. **Monitor Logs**: Watch for any permission-related errors

## Troubleshooting Checklist

- [ ] JWT token is valid and not expired
- [ ] JWT token includes required permissions in payload
- [ ] User account has the required permissions assigned
- [ ] Permission strings match exactly (case-sensitive)
- [ ] Services are running and healthy
- [ ] Network connectivity between services
- [ ] Database connections are working
- [ ] Redis cache is accessible

## Support

If you continue to experience issues:

1. Check the logs: `docker compose logs -f student-apigw`
2. Verify permissions in the auth service
3. Test with the provided test script
4. Check the JWT token payload for required permissions 