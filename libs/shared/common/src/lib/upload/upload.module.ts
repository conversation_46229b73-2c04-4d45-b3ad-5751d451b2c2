import { Module, DynamicModule, Global } from '@nestjs/common';
import { UploadService } from './upload.service';
import { StorageProvider } from './interfaces/storage-provider.interface';
import { S3StorageProvider } from './providers/s3-storage.provider';

export interface UploadModuleConfig {
  provider?: 's3'; // in future: 'cdn', 'local', etc.
  s3?: {
    endpoint: string;
    accessKey: string;
    secretKey: string;
    bucket: string;
    region: string;
    forcePathStyle?: boolean;
    publicEndpoint?: string;
  };
}

@Global()
@Module({})
export class UploadModule {
  static forRoot(config: UploadModuleConfig): DynamicModule {
    return {
      module: UploadModule,
      providers: [
        UploadService,
        {
          provide: 'UPLOAD_MODULE_CONFIG',
          useValue: config,
        },
        {
          provide: 'StorageProvider',
          useClass: S3StorageProvider, // could switch based on config.provider
        },
      ],
      exports: [UploadService, 'StorageProvider'],
    };
  }
}
