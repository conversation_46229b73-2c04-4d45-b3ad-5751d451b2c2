syntax = "proto3";

package student_documents;

// Service definition for student documents operations
service StudentDocumentsService {
  rpc UploadDocuments(UploadDocumentsRequest) returns (UploadDocumentsResponse);
  rpc GetStudentDocuments(GetStudentDocumentsRequest) returns (GetStudentDocumentsResponse);
  rpc GetDocument(GetDocumentRequest) returns (DocumentResponse);
  rpc UpdateDocumentVerification(UpdateDocumentVerificationRequest) returns (DocumentResponse);
  rpc DeleteDocument(DeleteDocumentRequest) returns (DeleteDocumentResponse);
  rpc GetDocumentsSummary(GetDocumentsSummaryRequest) returns (DocumentsSummaryResponse);
}

// Request/Response messages for uploading documents
message UploadDocumentsRequest {
  int32 student_id = 1;
  string sponsor_name = 2;
  bool take_dependents = 3;
  repeated string academic_sections = 4;
  repeated string proficiency_sections = 5;
  optional SponsorMetadata sponsor_metadata = 6;
  repeated Dependent dependents = 7;
  repeated Child children = 8;
  repeated FileUpload files = 9;
}

message FileUpload {
  string fieldname = 1;
  string filename = 2;
  string mimetype = 3;
  bytes data = 4;
  int32 size = 5;
}

message SponsorMetadata {
  string name = 1;
  optional string relationship = 2;
  optional string phone = 3;
  optional string email = 4;
}

message Dependent {
  string name = 1;
  string passport = 2;
}

message Child {
  string name = 1;
  string passport = 2;
}

message UploadDocumentsResponse {
  bool success = 1;
  string message = 2;
  repeated DocumentInfo documents = 3;
  int32 total_uploaded = 4;
  int32 failed_uploads = 5;
  repeated string errors = 6;
}

// Request/Response for getting student documents
message GetStudentDocumentsRequest {
  int32 student_id = 1;
  optional string section = 2;
  optional string field = 3;
  optional string verification_status = 4;
  optional bool include_inactive = 5;
}

message GetStudentDocumentsResponse {
  repeated DocumentInfo documents = 1;
}

// Request/Response for getting a specific document
message GetDocumentRequest {
  int32 student_id = 1;
  int32 document_id = 2;
}

message DocumentResponse {
  DocumentInfo document = 1;
}

// Request/Response for updating document verification
message UpdateDocumentVerificationRequest {
  int32 document_id = 1;
  string verification_status = 2;
  optional string notes = 3;
  optional string expiry_date = 4; // ISO date string
}

// Request/Response for deleting document
message DeleteDocumentRequest {
  int32 student_id = 1;
  int32 document_id = 2;
}

message DeleteDocumentResponse {
  string message = 1;
}

// Request/Response for documents summary
message GetDocumentsSummaryRequest {
  int32 student_id = 1;
}

message DocumentsSummaryResponse {
  map<string, SectionSummary> sections = 1;
}

message SectionSummary {
  int32 total = 1;
  int32 verified = 2;
  int32 pending = 3;
  int32 rejected = 4;
  repeated string fields = 5;
}

// Common document information structure
message DocumentInfo {
  int32 id = 1;
  int32 student_id = 2;
  string section = 3;
  string field = 4;
  string filename = 5;
  string url = 6;
  string mime_type = 7;
  int64 file_size = 8;
  string original_name = 9;
  string bucket = 10;
  string object_key = 11;
  optional DocumentMetadata metadata = 12;
  bool is_active = 13;
  optional string verification_status = 14;
  optional string expiry_date = 15; // ISO date string
  optional string notes = 16;
  string created_at = 17; // ISO datetime string
  string updated_at = 18; // ISO datetime string
}

message DocumentMetadata {
  optional int32 dependent_index = 1;
  optional int32 child_index = 2;
  optional string dependent_name = 3;
  optional string child_name = 4;
  optional string dependent_passport = 5;
  optional string child_passport = 6;
  map<string, string> additional_data = 7;
}

// Error handling
message ErrorResponse {
  string code = 1;
  string message = 2;
  repeated string details = 3;
}
